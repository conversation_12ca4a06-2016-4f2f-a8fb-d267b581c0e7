import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON> thức thanh toán",
  description: "<PERSON><PERSON><PERSON> hình thức thanh toán tại VNCM - Kho nhạc bản quyền và không bản quyền Việt Nam",
}

export default function PaymentMethodsPage() {
  return (
    <div className="w-full mx-auto py-8">
      <div className="bg-white p-8">
        <h1 className="text-3xl font-bold text-orange-500 mb-8 text-center">
          <PERSON><PERSON><PERSON> thức thanh toán
        </h1>

        <div className="prose prose-gray max-w-none">
          <section className="mb-8">
            <p className="text-gray-700 leading-relaxed mb-6 text-lg">
              Khi mua hàng khách hàng có thể thanh toán bằng nhiều hình thức khác nhau như:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <div className="bg-gradient-to-br from-orange-50 to-red-50 border border-orange-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold text-black">Thanh toán trực tuyến</h3>
                </div>
                <p className="text-gray-700 text-sm">
                  Trực tiếp trên website thông qua các cổng thanh toán
                </p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold text-black">Tiền mặt</h3>
                </div>
                <p className="text-gray-700 text-sm">
                  Thanh toán bằng tiền mặt khi giao hàng
                </p>
              </div>

              <div className="bg-gradient-to-br from-blue-50 to-cyan-50 border border-blue-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold text-black">Thu hộ</h3>
                </div>
                <p className="text-gray-700 text-sm">
                  Dịch vụ thu hộ tiền khi giao hàng
                </p>
              </div>

              <div className="bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold text-black">Chuyển khoản</h3>
                </div>
                <p className="text-gray-700 text-sm">
                  Chuyển khoản ngân hàng trực tiếp
                </p>
              </div>

              <div className="bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-indigo-500 rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold text-black">Máy cà thẻ</h3>
                </div>
                <p className="text-gray-700 text-sm">
                  Thanh toán bằng thẻ ATM/Visa/Mastercard
                </p>
              </div>

              <div className="bg-gradient-to-br from-pink-50 to-rose-50 border border-pink-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-pink-500 rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold text-black">Trả góp</h3>
                </div>
                <p className="text-gray-700 text-sm">
                  Trả góp bằng thẻ tín dụng
                </p>
              </div>
            </div>
          </section>

          <section className="mb-8">
            <div className="bg-gradient-to-r from-orange-50 to-red-50 border-l-4 border-orange-500 rounded-lg p-8">
              <h2 className="text-2xl font-semibold text-orange-600 mb-6 flex items-center">
                <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                Thông tin tài khoản ngân hàng
              </h2>
              
              <div className="bg-white rounded-lg p-6 shadow-sm border border-orange-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <div>
                        <span className="font-semibold text-gray-800">Ngân hàng:</span>
                        <p className="text-gray-700 font-medium">Ngân Hàng Quân Đội MB Bank</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <div>
                        <span className="font-semibold text-gray-800">Chủ tài khoản:</span>
                        <p className="text-gray-700 font-medium">DZUS CO LTD</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <div>
                        <span className="font-semibold text-gray-800">Số tài khoản:</span>
                        <p className="text-gray-700 font-bold text-lg text-orange-600">*********</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <div>
                        <span className="font-semibold text-gray-800">Công ty:</span>
                        <p className="text-gray-700 font-medium">Công Ty TNHH DZUS</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <div>
                        <span className="font-semibold text-gray-800">Chi nhánh:</span>
                        <p className="text-gray-700 font-medium">Thăng Long</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-yellow-800">Lưu ý quan trọng</p>
                      <p className="text-sm text-yellow-700 mt-1">
                        Khi chuyển khoản, vui lòng ghi rõ nội dung chuyển khoản để chúng tôi có thể xác nhận thanh toán nhanh chóng.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="mb-8">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-blue-600 mb-4">Hướng dẫn thanh toán</h3>
              <ul className="list-decimal list-inside text-gray-700 space-y-2">
                <li>Chọn sản phẩm và thêm vào giỏ hàng</li>
                <li>Chọn hình thức thanh toán phù hợp</li>
                <li>Điền đầy đủ thông tin giao hàng</li>
                <li>Xác nhận đơn hàng và tiến hành thanh toán</li>
                <li>Nhận xác nhận đơn hàng qua email</li>
              </ul>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
}
