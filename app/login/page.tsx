"use client"
import type React from "react"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import GoogleLoginButton from "@/components/googleloginbutton"
import { apiLogin } from "@/api"
import { useSetLoading, useIsLoading } from "@/store/authStore"
import Link from "next/link"

const loginSchema = z.object({
  email: z
    .string()
    .min(1, "Email là bắt buộc")
    .email("Email không hợp lệ"),
  password: z
    .string()
    .min(1, "Mật khẩu là bắt buộc")
    .min(6, "<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự"),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const [error, setError] = useState("")
  const router = useRouter()
  const setLoading = useSetLoading()
  const isLoading = useIsLoading()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      password: "",
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    setLoading(true)
    setError("")
    try {
      await apiLogin({ email: data.email, password: data.password })
      router.push("/")
    } catch (err: any) {
      setError(err.message || "Đăng nhập thất bại")
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleLoginSuccess = () => {
    router.push("/")
  }

  return (
    <div className="flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md">
        <Card className="bg-white rounded-[32px] border p-8">
          <CardHeader className="text-center pb-8">
            <CardTitle className="text-2xl font-bold text-black">CHÀO MỪNG ĐẾN VỚI VNCM</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <GoogleLoginButton
                onLoginSuccess={handleGoogleLoginSuccess}
                className="w-full rounded-full h-12 bg-black text-white"
              >
                <img src="/icon/google.svg" alt="Google logo" className="mr-2 h-6 w-6" />
                Đăng nhập với Google
              </GoogleLoginButton>

              {error && <div className="text-red-600 text-sm text-center">{error}</div>}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Email"
                    disabled={isLoading}
                    className="rounded-full h-12 px-4 bg-white border border-gray-300 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-gray-300"
                    {...register("email")}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1 text-center">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Mật khẩu"
                    disabled={isLoading}
                    className="rounded-full h-12 px-4 bg-white border border-gray-300 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-gray-300"
                    {...register("password")}
                  />
                  {errors.password && (
                    <p className="text-red-500 text-sm mt-1 text-center">{errors.password.message}</p>
                  )}
                </div>

                <div className="text-center text-sm">
                  <Link href="/forgot-password" className="text-black hover:underline">
                    Quên mật khẩu
                  </Link>
                </div>

                <div className="flex space-x-4 pt-4">
                  <Link href="/register" className="flex-1">
                    <Button
                      type="button"
                      variant="outline"
                      disabled={isLoading}
                      className="w-full rounded-full h-12 border-black text-black hover:bg-gray-100 bg-transparent"
                    >
                      ĐĂNG KÝ
                    </Button>
                  </Link>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1 rounded-full h-12 bg-[#A8FF00] text-black hover:bg-[#90e000] disabled:opacity-50"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Đang đăng nhập...
                      </>
                    ) : (
                      "ĐĂNG NHẬP"
                    )}
                  </Button>
                </div>
              </form>

              <div className="text-center text-sm text-gray-600 mt-6">
                <Link href="/dieu-khoan-dich-vu" className="text-black hover:underline">
                  Điều khoản sử dụng
                </Link>{" "}
                <span className="mx-2">|</span>{" "}
                <Link href="/chinh-sach-bao-mat" className="text-black hover:underline">
                  Chính sách riêng tư
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
