'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Music, Users, Disc3, Heart, TrendingUp, Download } from 'lucide-react'
import api from '@/api/axios'
import { apiGetSongs } from '@/api/apiSong'
import { apiGetUsers } from '@/api/apiUser'
import { apiGetGenres } from '@/api/apiGenre'
import { apiGetMoods } from '@/api/apiMood'
import { apiGetArtists } from '@/api/apiArtist'

interface DashboardStats {
  totalSongs: number
  totalUsers: number
  totalGenres: number
  totalMoods: number
  totalArtists: number
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalSongs: 0,
    totalUsers: 0,
    totalGenres: 0,
    totalMoods: 0,
    totalArtists: 0
  })
  const [loading, setLoading] = useState(true)

  const fetchCount = async (endpoint: string, fallbackApi: () => Promise<any>): Promise<number> => {
    try {
      const response = await api.get(`${endpoint}/count`)
      return response.data.count || 0
    } catch (error) {
      try {
        const fallbackResponse = await fallbackApi()
        return fallbackResponse.total || 0
      } catch (fallbackError) {
        console.error(`Error fetching count for ${endpoint}:`, error, fallbackError)
        return 0
      }
    }
  }

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        const [songsCount, usersCount, genresCount, moodsCount, artistsCount] = await Promise.all([
          fetchCount('/song', () => apiGetSongs({ limit: 100 })),
          fetchCount('/user', () => apiGetUsers({ limit: 100 })),
          fetchCount('/genre', () => apiGetGenres({ limit: 100 })),
          fetchCount('/mood', () => apiGetMoods({ limit: 100 })),
          fetchCount('/artist', () => apiGetArtists({ limit: 100 }))
        ])

        setStats({
          totalSongs: songsCount,
          totalUsers: usersCount,
          totalGenres: genresCount,
          totalMoods: moodsCount,
          totalArtists: artistsCount
        })
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statCards = [
    {
      title: 'Tổng số bài hát',
      value: stats.totalSongs,
      icon: Music,
      color: 'from-orange-50 to-red-50',
      borderColor: 'border-orange-200',
      iconColor: 'text-orange-500',
      dotColor: 'bg-orange-500'
    },
    {
      title: 'Tổng số người dùng',
      value: stats.totalUsers,
      icon: Users,
      color: 'from-blue-50 to-indigo-50',
      borderColor: 'border-blue-200',
      iconColor: 'text-blue-500',
      dotColor: 'bg-blue-500'
    },
    {
      title: 'Tổng số nghệ sĩ',
      value: stats.totalArtists,
      icon: TrendingUp,
      color: 'from-purple-50 to-pink-50',
      borderColor: 'border-purple-200',
      iconColor: 'text-purple-500',
      dotColor: 'bg-purple-500'
    },
    {
      title: 'Tổng số thể loại',
      value: stats.totalGenres,
      icon: Disc3,
      color: 'from-green-50 to-emerald-50',
      borderColor: 'border-green-200',
      iconColor: 'text-green-500',
      dotColor: 'bg-green-500'
    },
    {
      title: 'Tổng số mood',
      value: stats.totalMoods,
      icon: Heart,
      color: 'from-pink-50 to-rose-50',
      borderColor: 'border-pink-200',
      iconColor: 'text-pink-500',
      dotColor: 'bg-pink-500'
    },
  ]

  return (
    <div className="space-y-6 pb-20">
      <div>
        <h1 className="text-3xl font-bold text-orange-500 mb-8 text-center">
          Thống kê hệ thống
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {statCards.map((card, index) => {
            const IconComponent = card.icon
            return (
              <Card key={index} className={`bg-gradient-to-br ${card.color} ${card.borderColor} hover:shadow-md transition-shadow`}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">
                    {card.title}
                  </CardTitle>
                  <IconComponent className={`h-4 w-4 ${card.iconColor}`} />
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 ${card.dotColor} rounded-full`}></div>
                    <div className="text-2xl font-bold text-black">
                      {loading ? '...' : typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Cập nhật lần cuối: {new Date().toLocaleDateString('vi-VN')}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </div>
  )
}
