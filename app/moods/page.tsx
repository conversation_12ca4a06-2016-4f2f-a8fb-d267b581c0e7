"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { apiGetMoods, Mood } from "@/api/apiMood"

export default function MoodsPage() {
  const [moods, setMoods] = useState<Mood[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchMoods = async () => {
      try {
        setLoading(true)
        const response = await apiGetMoods()
        setMoods(response.items)
      } catch (err: any) {
        setError(err.message || 'Something went wrong')
      } finally {
        setLoading(false)
      }
    }

    fetchMoods()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-black mb-8">TÂM TRẠNG</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-0">
                <Skeleton className="w-full h-48" />
                <div className="p-4">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-black mb-8">TÂM TRẠNG</h1>
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
          >
            Thử lại
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-black mb-8">TÂM TRẠNG</h1>

      {moods.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-600">Không có tâm trạng nào được tìm thấy.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {moods.map((mood) => (
            <Link key={mood.id} href={`/moods/${mood.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <CardContent className="p-0">
                  <div className="relative">
                    <div
                      className="w-full h-48 flex items-center justify-center bg-gradient-to-br from-pink-400 to-orange-400"
                    >
                      <span className="text-6xl">
                        🎵
                      </span>
                    </div>
                  </div> 
                  <div className="p-4">
                    <h3 className="font-semibold text-lg text-black mb-1 truncate">
                      {mood.name}
                    </h3>
                    {mood.description && (
                      <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                        {mood.description}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </div>
  )
}
