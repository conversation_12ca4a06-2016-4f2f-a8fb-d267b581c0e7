import type React from "react"
import type { Metadata } from "next"
import "./globals.css"
import { ClientLayout } from "@/components/client-layout"
import { GoogleOAuthProvider } from '@react-oauth/google';


export const metadata: Metadata = {
  title: {
    default: "VNCM - Kho Nhạc Bản Quyền & Không Bản Quyền Việt Nam",
    template: "%s | VNCM"
  },
  description: "VNCM - Kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao. Tải nhạc miễn phí, nghe nhạc online, khám phá âm nhạc Việt Nam và quốc tế.",
  applicationName: "VNCM",
  keywords: [
    "VNCM",
    "nhạc bản quyền",
    "nhạc không bản quyền",
    "royalty free music",
    "kho nhạc Việt Nam",
    "tải nhạc miễn ph<PERSON>",
    "nghe nhạc online",
    "nhạc chất lượng cao",
    "thư viện nhạc",
    "âm nhạc Việt Nam",
    "nhạc quốc tế",
    "download nhạc",
    "streaming music",
    "nhạc MP3",
    "nhạc WAV",
    "Vietnamese music library",
    "royalty free music Vietnam",
    "copyright free music",
    "music download",
    "music streaming",
    "high quality music",
    "Vietnamese songs",
    "Asian music",
    "free music download",
    "music platform Vietnam"
  ],
  authors: [
    { name: "VNCM Team", url: "https://vncm.net/about" },
    { name: "VNCM Music Library", url: "https://vncm.net" }
  ],
  creator: "VNCM - Kho Nhạc Việt Nam",
  publisher: "VNCM Platform",
  category: "Music & Audio",
  classification: "Music Library",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://vncm.net'),
  alternates: {
    canonical: '/',
    languages: {
      'vi-VN': '/',
    },
  },
  openGraph: {
    title: "VNCM - Kho Nhạc Bản Quyền & Không Bản Quyền Việt Nam",
    description: "Kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao. Tải nhạc miễn phí, nghe nhạc online tại VNCM.",
    url: '/',
    siteName: 'VNCM - Kho Nhạc Việt Nam',
    images: [
      {
        url: '/share.png',
        width: 1200,
        height: 630,
        alt: 'VNCM - Kho Nhạc Bản Quyền & Không Bản Quyền Việt Nam',
        type: 'image/png',
      },
    ],
    locale: 'vi_VN',
    type: 'website',
    countryName: 'Vietnam',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@vncm_net',
    creator: '@vncm_net',
    title: "VNCM - Kho Nhạc Bản Quyền & Không Bản Quyền Việt Nam",
    description: "Kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao. Tải nhạc miễn phí, nghe nhạc online.",
    images: {
      url: '/share.png',
      alt: 'VNCM - Kho Nhạc Việt Nam',
    },
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
  },
  manifest: '/site.webmanifest',
}

const jsonLdSchemas = [
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://vncm.net/#organization",
    "name": "VNCM",
    "alternateName": ["Kho Nhạc Việt Nam", "VNCM Music Library"],
    "url": "https://vncm.net",
    "logo": {
      "@type": "ImageObject",
      "url": "https://vncm.net/logo/vncm.png",
      "width": 512,
      "height": 512
    },
    "image": "https://vncm.net/share.png",
    "description": "Kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao",
    "foundingDate": "2024",
    "foundingLocation": {
      "@type": "Place",
      "name": "Vietnam"
    },
    "areaServed": {
      "@type": "Country",
      "name": "Vietnam"
    },
    "knowsLanguage": ["vi", "en"],
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["Vietnamese", "English"],
        "url": "https://vncm.net/contact"
      },
      {
        "@type": "ContactPoint",
        "contactType": "technical support",
        "availableLanguage": ["Vietnamese", "English"],
        "url": "https://vncm.net/support"
      }
    ],
    "sameAs": [
      "https://facebook.com/vncm.net",
      "https://twitter.com/vncm_net",
      "https://instagram.com/vncm_net",
      "https://youtube.com/@vncm_net"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "VNCM Music Library",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Music Library & Streaming",
            "description": "Royalty-free and copyrighted music library with high-quality downloads"
          }
        }
      ]
    }
  },
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": "https://vncm.net/#website",
    "url": "https://vncm.net",
    "name": "VNCM - Kho Nhạc Bản Quyền & Không Bản Quyền Việt Nam",
    "description": "Kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao",
    "publisher": {
      "@id": "https://vncm.net/#organization"
    },
    "inLanguage": "vi-VN",
    "potentialAction": [
      {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": "https://vncm.net/search?q={search_term_string}"
        },
        "query-input": "required name=search_term_string"
      }
    ]
  },
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "VNCM Music Library",
    "url": "https://vncm.net",
    "applicationCategory": "MultimediaApplication",
    "applicationSubCategory": "Music Library",
    "operatingSystem": "Web Browser",
    "description": "VNCM - Kho nhạc đa dạng với bài hát bản quyền và không bản quyền chất lượng cao",
    "screenshot": "https://vncm.net/share.png",
    "softwareVersion": "1.0",
    "datePublished": "2024-01-01",
    "author": {
      "@id": "https://vncm.net/#organization"
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "VND",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "Royalty-Free Music",
      "Copyrighted Music",
      "High-Quality Downloads",
      "Music Streaming",
      "Music Search",
      "Multiple Formats (MP3, WAV)"
    ]
  }
]

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi" dir="ltr">
      <head>
        {jsonLdSchemas.map((schema, index) => (
          <script
            key={`jsonld-${index}`}
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(schema, null, 0)
            }}
          />
        ))}

        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        <link rel="dns-prefetch" href="//api.vncm.net" />
        <link rel="dns-prefetch" href="//cdn.vncm.net" />
      </head>
      <body className="font-sf-pro">
        <GoogleOAuthProvider clientId="511099088680-7vtb0agv25qapk0130kanq2d8qdq0rmk.apps.googleusercontent.com">
          <ClientLayout>
            {children}
          </ClientLayout>
        </GoogleOAuthProvider>
      </body>
    </html>
  )
}
