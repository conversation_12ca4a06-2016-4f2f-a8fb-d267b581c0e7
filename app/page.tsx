import { Metadata } from 'next'
import HomePageClient from '@/components/home/<USER>'

export const metadata: Metadata = {
  title: 'Trang Chủ - Kho Nhạc Bản Quyền & Không Bản Quyền',
  description: 'Khám phá kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao. Tải nhạc miễn phí, nghe nhạc online tại VNCM.',
  keywords: [
    'trang chủ VNCM',
    'kho nhạc việt nam',
    'nhạc bản quyền',
    'nhạc không bản quyền',
    'tải nhạc miễn phí',
    'nghe nhạc online'
  ],
  openGraph: {
    title: 'VNCM - Kho Nhạc Bản Quyền & Không Bản Quyền Việt Nam',
    description: '<PERSON>h<PERSON><PERSON> phá kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao.',
    url: '/',
    type: 'website',
  },
}

const homePageJsonLd = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  "@id": "https://vncm.net/#homepage",
  "url": "https://vncm.net",
  "name": "Trang Chủ - VNCM",
  "description": "Kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao",
  "isPartOf": {
    "@id": "https://vncm.net/#website"
  },
  "about": {
    "@type": "Thing",
    "name": "Music Library"
  },
  "mainEntity": {
    "@type": "MusicGroup",
    "name": "VNCM Music Collection",
    "description": "Comprehensive collection of royalty-free and copyrighted music"
  }
}

export default function page() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(homePageJsonLd, null, 0)
        }}
      />
      <HomePageClient />
    </>
  )
}
