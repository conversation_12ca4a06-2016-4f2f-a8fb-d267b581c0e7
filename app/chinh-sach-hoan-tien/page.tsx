import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Chính sách hoàn tiền",
  description: "<PERSON>ính sách hoàn tiền của VNCM - Kho nhạc bản quyền và không bản quyền Việt Nam",
}

export default function RefundPolicyPage() {
  return (
    <div className="w-full mx-auto py-8">
      <div className="bg-white p-8">
        <h1 className="text-3xl font-bold text-orange-500 mb-8 text-center">
          Ch<PERSON>h sách hoàn tiền
        </h1>

        <div className="prose prose-gray max-w-none">
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-black mb-4">1. Điều kiện trả hàng & hoàn tiền</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Chúng tôi chấp nhận yêu c<PERSON>u trả hàng và hoàn tiền trong các trường hợp sau:
            </p>
            <ul className="list-disc list-inside text-gray-700 mb-4 space-y-3">
              <li>Người mua đã thanh toán nhưng không nhận được sản phẩm trong 24 vòng giờ.</li>
              <li>Sản phẩm bị lỗi hoặc không đúng mô tả.</li>
              <li>Sản phẩm Người mua nhận được khác biệt một cách rõ rệt so với thông tin mà Người bán cung cấp.</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-black mb-4">2. Điều kiện từ chối trả hàng & hoàn tiền</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Chúng tôi không hỗ trợ hoàn tiền nếu đó là lý do cá nhân của khách hàng, khách hàng có thể trải nghiệm những sản phẩm miễn phí dùng thử để nắm được chất lượng trước khi quyết định mua hàng.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-black mb-4">3. Quyền lợi của khách hàng</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Khách hàng có thể lựa chọn đổi ngang sang một sản phẩm khác phù hợp hơn có cùng giá tiền trong vòng 1 tháng kể từ ngày nhận sản phẩm.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-black mb-4">4. Liên lạc giữa người bán và người mua</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Chúng tôi luôn mong muốn có thể trao đổi với khách hàng bất cứ phát sinh nào trong giao dịch. Do vậy khi gặp vấn đề với đơn hàng hãy liên hệ ngay hotline <strong className="text-orange-600">0855830222</strong> hoặc <strong className="text-orange-600">0911830222</strong> để được hỗ trợ giải quyết trong thời gian sớm nhất.
            </p>
          </section>

          <section className="mb-8">
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
              <h2 className="text-2xl font-semibold text-orange-600 mb-4">Thông tin liên hệ</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                Mọi thắc mắc khác, vui lòng liên hệ:
              </p>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-700">
                    <strong>Hotline chăm sóc khách hàng:</strong> 
                    <a href="tel:0855830222" className="text-orange-600 hover:text-orange-700 ml-2 font-semibold">0855830222</a> hoặc 
                    <a href="tel:0911830222" className="text-orange-600 hover:text-orange-700 ml-2 font-semibold">0911830222</a>
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-700">
                    <strong>Website:</strong> 
                    <a href="https://vncm.net" target="_blank" rel="noopener noreferrer" className="text-orange-600 hover:text-orange-700 ml-2 underline">
                      https://vncm.net
                    </a>
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-700">
                    <strong>Email:</strong> 
                    <a href="mailto:<EMAIL>" className="text-orange-600 hover:text-orange-700 ml-2 underline">
                      <EMAIL>
                    </a>
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-700">
                    <strong>Fanpage:</strong> 
                    <a href="https://www.facebook.com/vietnamcopyrightmusic" target="_blank" rel="noopener noreferrer" className="text-orange-600 hover:text-orange-700 ml-2 underline">
                      https://www.facebook.com/vietnamcopyrightmusic
                    </a>
                  </span>
                </div>
              </div>
            </div>
          </section>

          <section className="mb-8">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-blue-600 mb-3">Lưu ý quan trọng</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Vui lòng giữ lại hóa đơn và thông tin giao dịch để thuận tiện cho việc xử lý yêu cầu hoàn tiền</li>
                <li>Thời gian xử lý hoàn tiền thường từ 3-7 ngày làm việc tùy thuộc vào phương thức thanh toán</li>
                <li>Chúng tôi khuyến khích khách hàng liên hệ trước khi gửi yêu cầu hoàn tiền để được tư vấn tốt nhất</li>
              </ul>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
}
