"use client"
import type React from "react"
import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import Link from "next/link"
import { userRegister } from "@/api"
import { useSetLoading, useIsLoading } from "@/store/authStore"
import GoogleLoginButton from "@/components/googleloginbutton"

const registerSchema = z.object({
  email: z
    .string()
    .min(1, "Email là bắt buộc")
    .email("Email không hợp lệ"),
  name: z
    .string()
    .min(1, "Tên là bắt buộc")
    .min(2, "Tên phải có ít nhất 2 ký tự"),
  password: z
    .string()
    .min(1, "Mật khẩu là bắt buộc")
    .min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
  confirmPassword: z
    .string()
    .min(1, "Xác nhận mật khẩu là bắt buộc"),
  agreeToTerms: z
    .boolean()
    .refine((val) => val === true, {
      message: "Bạn phải đồng ý với điều khoản sử dụng",
    })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Mật khẩu xác nhận không khớp",
  path: ["confirmPassword"],
})

type RegisterFormData = z.infer<typeof registerSchema>

export default function RegisterPage() {
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const router = useRouter()
  const setLoading = useSetLoading()
  const loading = useIsLoading()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: "",
      name: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    },
    mode: "onChange",
  })

  const onSubmit = async (data: RegisterFormData) => {
    setLoading(true)
    setError(null)

    try {
      await userRegister({
        name: data.name,
        email: data.email,
        password: data.password,
      })
      setSuccess(true)
      setTimeout(() => {
        router.push("/")
      }, 2000)
    } catch (err: any) {
      setError(err.message || "Đăng ký thất bại")
    } finally {
      setLoading(false)
    }
  }
  const handleGoogleLoginSuccess = () => {
    router.push("/")
  }
  if (success) {
    return (
      <div className="flex items-center justify-center min-h-full">
        <Card className="w-full max-w-md bg-white rounded-[32px] border-none shadow-none p-8">
          <CardContent className="pt-6 text-center">
            <h2 className="text-2xl font-bold text-black mb-4">Đăng ký thành công!</h2>
            <p className="text-gray-600 mt-2">Đang chuyển hướng về trang chủ...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md">
        <Card className="bg-white rounded-[32px] border shadow-none p-8">
          <CardHeader className="text-center pb-8">
            <CardTitle className="text-2xl font-bold text-black">ĐĂNG KÝ TÀI KHOẢN</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <GoogleLoginButton
                onLoginSuccess={handleGoogleLoginSuccess}
                className="w-full rounded-full h-12 bg-black text-white"
              >
                <img src="/icon/google.svg" alt="Google logo" className="mr-2 h-6 w-6" />
                Đăng ký với Google
              </GoogleLoginButton>
              {error && (
                <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">{error}</div>
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Email"
                    disabled={loading}
                    className="rounded-full text-center h-12 px-4 bg-white border border-gray-300 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-gray-300"
                    {...register("email")}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1 text-center">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <Input
                    id="name"
                    type="text"
                    placeholder="Tên"
                    disabled={loading}
                    className="rounded-full text-center h-12 px-4 bg-white border border-gray-300 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-gray-300"
                    {...register("name")}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1 text-center">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Mật khẩu"
                    disabled={loading}
                    className="rounded-full text-center h-12 px-4 bg-white border border-gray-300 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-gray-300"
                    {...register("password")}
                  />
                  {errors.password && (
                    <p className="text-red-500 text-sm mt-1 text-center">{errors.password.message}</p>
                  )}
                </div>

                <div>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="Nhập lại mật khẩu"
                    disabled={loading}
                    className="rounded-full text-center h-12 px-4 bg-white border border-gray-300 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-gray-300"
                    {...register("confirmPassword")}
                  />
                  {errors.confirmPassword && (
                    <p className="text-red-500 text-sm mt-1 text-center">{errors.confirmPassword.message}</p>
                  )}
                </div>

                <div>
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id="terms"
                      disabled={loading}
                      className="border-gray-400 data-[state=checked]:bg-black data-[state=checked]:text-white"
                      {...register("agreeToTerms")}
                    />
                    <label
                      htmlFor="terms"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black"
                    >
                      Tôi đồng ý với các{" "}
                      <Link href="/dieu-khoan-dich-vu" className="text-[#8A2BE2] hover:underline">
                        điều khoản
                      </Link>{" "}
                      của VNCM
                    </label>
                  </div>
                  {errors.agreeToTerms && (
                    <p className="text-red-500 text-sm mt-1 text-center">{errors.agreeToTerms.message}</p>
                  )}
                </div>

                <div className="flex space-x-4 pt-4">
                  <Link href="/login" className="flex-1">
                    <Button
                      type="button"
                      variant="outline"
                      disabled={loading}
                      className="w-full rounded-full h-12 border-black text-black"
                    >
                      ĐĂNG NHẬP
                    </Button>
                  </Link>
                  <Button
                    type="submit"
                    disabled={loading}
                    className="flex-1 rounded-full h-12 bg-[#A8FF00] text-black hover:bg-[#90e000] disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Đang tạo tài khoản...
                      </>
                    ) : (
                      "ĐĂNG KÝ"
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
