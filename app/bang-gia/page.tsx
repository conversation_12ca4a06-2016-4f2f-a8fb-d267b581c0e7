import { PricingClient } from "@/components/pricing/pricing-client"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Bảng Giá - VNCM | Gói Cá Nhân 299K/tháng, Thương Mại 599K/tháng",
  description: "Khám phá các gói dịch vụ của VNCM - Gói Cá Nhân 2.99 triệu/năm, Gói Thương Mại 5.99 triệu/năm. Tạo nhạc AI, thư viện nhạc premium, hỗ trợ 24/7. Tiết kiệm 17% khi đăng ký hàng năm.",
  keywords: [
    "bảng giá VNCM",
    "gói dịch vụ âm nhạc",
    "pricing music platform",
    "gói cá nhân 299k",
    "gói thương mại 599k",
    "gói doanh nghiệp",
    "AI music pricing",
    "Vietnamese music platform pricing",
    "music subscription Vietnam",
    "creative music platform pricing"
  ],
  authors: [{ name: "VNCM Team" }],
  creator: "VNCM",
  publisher: "VNCM",
  alternates: {
    canonical: "/pricing",
  },
  openGraph: {
    title: "Bảng Giá - VNCM | Chọn Gói Phù Hợp Với Bạn",
    description: "Từ người sáng tạo cá nhân đến doanh nghiệp lớn, VNCM có giải pháp phù hợp cho mọi nhu cầu. Gói Cá Nhân từ 299K/tháng, Gói Thương Mại từ 599K/tháng. Tiết kiệm 17% khi đăng ký hàng năm.",
    type: "website",
    url: "/pricing",
    siteName: "VNCM",
    images: [
      {
        url: "/share.png",
        width: 1200,
        height: 630,
        alt: "VNCM Pricing Plans - Gói Cá Nhân, Thương Mại, Doanh Nghiệp",
      },
    ],
    locale: "vi_VN",
  },
  twitter: {
    card: "summary_large_image",
    title: "Bảng Giá - VNCM | Chọn Gói Phù Hợp Với Bạn",
    description: "Từ người sáng tạo cá nhân đến doanh nghiệp lớn, VNCM có giải pháp phù hợp cho mọi nhu cầu. Tiết kiệm 17% khi đăng ký hàng năm.",
    images: ["/share.png"],
    creator: "@vncm_platform",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export const pricingPlans = [
  {
    id: "personal",
    name: "Gói Cá Nhân",
    description: "Hoàn hảo cho người sáng tạo cá nhân",
    price: {
      monthly: 299000,
      yearly: 2990000,
    },
    originalPrice: {
      yearly: 3588000,
    },
    duration: "1 năm",
    popular: true,
    features: [
      "Xuất bản nội dung ở khắp mọi nơi",
      "Kiếm tiền cho 1 kênh trên mỗi nền tảng",
      "Tải xuống không giới hạn",
      "Truy cập khóa học cho người sáng tạo",
      "Hỗ trợ email 24/7",
      "Thư viện nhạc premium",
    ],
    buttonText: "Bắt đầu ngay",
    tier: 1,
  },
  {
    id: "commercial",
    name: "Gói Thương Mại",
    description: "Dành cho doanh nghiệp nhỏ và vừa",
    price: {
      monthly: 599000,
      yearly: 5990000,
    },
    originalPrice: {
      yearly: 7188000,
    },
    duration: "Vĩnh viễn",
    popular: false,
    features: [
      "Mọi thứ trong gói cá nhân",
      "Kiếm tiền cho 3 kênh trên mỗi nền tảng",
      "Xuất bản nội dung cho khách hàng",
      "Sử dụng để chạy quảng cáo online",
      "Hỗ trợ ưu tiên",
      "Quản lý nhiều dự án",
      "API truy cập",
    ],
    buttonText: "Nâng cấp ngay",
    tier: 2,
  },
  {
    id: "enterprise",
    name: "Gói Doanh Nghiệp Lớn",
    description: "Giải pháp tùy chỉnh cho doanh nghiệp",
    price: {
      monthly: "Liên hệ",
      yearly: "Liên hệ",
    },
    duration: "Tùy chỉnh",
    popular: false,
    features: [
      "Mọi thứ trong gói thương mại",
      "Không giới hạn kênh kiếm tiền",
      "Giải pháp tùy chỉnh",
      "Hỗ trợ dedicated account manager",
      "SLA 99.9% uptime",
      "Tích hợp enterprise",
      "Đào tạo và onboarding",
      "Báo cáo và phân tích nâng cao",
    ],
    buttonText: "Liên hệ tư vấn",
    tier: 3,
  },
]

const jsonLd = [
  {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "VNCM - Vietnamese Creative Music Platform",
    "description": "Nền tảng âm nhạc sáng tạo Việt Nam với công nghệ AI tiên tiến",
    "brand": {
      "@type": "Brand",
      "name": "VNCM"
    },
    "offers": [
      {
        "@type": "Offer",
        "name": "Gói Cá Nhân",
        "description": "Hoàn hảo cho người sáng tạo cá nhân",
        "price": "2990000",
        "priceCurrency": "VND",
        "priceValidUntil": "2025-12-31",
        "availability": "https://schema.org/InStock",
        "url": "https://vncm.com/pricing",
        "seller": {
          "@type": "Organization",
          "name": "VNCM"
        }
      },
      {
        "@type": "Offer",
        "name": "Gói Thương Mại",
        "description": "Dành cho doanh nghiệp nhỏ và vừa",
        "price": "5990000",
        "priceCurrency": "VND",
        "priceValidUntil": "2025-12-31",
        "availability": "https://schema.org/InStock",
        "url": "https://vncm.com/pricing",
        "seller": {
          "@type": "Organization",
          "name": "VNCM"
        }
      },
      {
        "@type": "Offer",
        "name": "Gói Doanh Nghiệp Lớn",
        "description": "Giải pháp tùy chỉnh cho doanh nghiệp",
        "price": "0",
        "priceCurrency": "VND",
        "priceValidUntil": "2025-12-31",
        "availability": "https://schema.org/InStock",
        "url": "https://vncm.com/pricing",
        "seller": {
          "@type": "Organization",
          "name": "VNCM"
        }
      }
    ],
    "provider": {
      "@type": "Organization",
      "name": "VNCM",
      "url": "https://vncm.com",
      "logo": "https://vncm.com/logo/vncm.png"
    }
  },
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Trang chủ",
        "item": "https://vncm.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Bảng giá",
        "item": "https://vncm.com/pricing"
      }
    ]
  },
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "VNCM",
    "alternateName": "Vietnamese Creative Music",
    "url": "https://vncm.com",
    "logo": "https://vncm.com/logo/vncm.png",
    "description": "Nền tảng âm nhạc sáng tạo Việt Nam với công nghệ AI tiên tiến",
    "foundingDate": "2024",
    "sameAs": [
      "https://facebook.com/vncm",
      "https://twitter.com/vncm_platform"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["Vietnamese", "English"]
    }
  },
  {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Gói cá nhân có những tính năng gì?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Gói cá nhân bao gồm xuất bản nội dung ở khắp mọi nơi, kiếm tiền cho 1 kênh trên mỗi nền tảng, tải xuống không giới hạn, truy cập khóa học cho người sáng tạo, hỗ trợ email 24/7, và thư viện nhạc premium."
        }
      },
      {
        "@type": "Question",
        "name": "Có thể hủy gói dịch vụ không?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Có, bạn có thể hủy gói dịch vụ bất kỳ lúc nào. Chúng tôi cung cấp chính sách hoàn tiền linh hoạt cho khách hàng."
        }
      },
      {
        "@type": "Question",
        "name": "Gói thương mại khác gì so với gói cá nhân?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Gói thương mại bao gồm tất cả tính năng của gói cá nhân, cộng thêm kiếm tiền cho 3 kênh trên mỗi nền tảng, xuất bản nội dung cho khách hàng, sử dụng để chạy quảng cáo online, hỗ trợ ưu tiên, quản lý nhiều dự án và API truy cập."
        }
      },
      {
        "@type": "Question",
        "name": "Có hỗ trợ khách hàng không?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Có, chúng tôi cung cấp hỗ trợ email 24/7 cho tất cả gói dịch vụ. Gói thương mại và doanh nghiệp có hỗ trợ ưu tiên và dedicated account manager."
        }
      }
    ]
  }
]

export default function PricingPage() {
  return (
    <>
      {jsonLd.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
        />
      ))}

      <PricingClient />
    </>
  )
}
