"use client"
import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Music } from "lucide-react"
import { apiGetSongs, Song } from "@/api/apiSong"
import SongListItem from "@/components/home/<USER>"
import SongListSkeleton from "@/components/home/<USER>"

export default function ArtistDetailPage() {
  const params = useParams()
  const slug = params.slug as string

  const [songs, setSongs] = useState<Song[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [artistName, setArtistName] = useState<string>("")

  useEffect(() => {
    const fetchSongs = async () => {
      try {
        setLoading(true)
        const response = await apiGetSongs({
          artistSlug: slug,
          limit: 50
        })
        setSongs(response.items)

        if (response.items.length > 0 && response.items[0].artistLst.length > 0) {
          const artist = response.items[0].artistLst.find(a => a.slug === slug)
          if (artist) {
            setArtistName(artist.name)
          }
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch songs')
        setSongs([])
      } finally {
        setLoading(false)
      }
    }

    if (slug) {
      fetchSongs()
    }
  }, [slug])



  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Skeleton className="h-12 w-64 mb-4" />
          <Skeleton className="h-6 w-96 mb-2" />
          <Skeleton className="h-4 w-48" />
        </div>
        <div className="bg-white overflow-hidden">
          <SongListSkeleton />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.history.back()}>
            Quay lại
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div>
        <h2 className="text-2xl font-bold text-black mb-6">
          {artistName ? `Bài hát của ${artistName}` : 'Bài hát'}
        </h2>

        <div className="bg-white overflow-hidden">
          {loading ? (
            <SongListSkeleton />
          ) : songs.length === 0 ? (
            <div className="text-center py-12">
              <Music className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy bài hát</h3>
              <p className="text-gray-500">Nghệ sĩ này chưa có bài hát nào.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {songs.map((song) => (
                <SongListItem key={song.id} song={song} songs={songs} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}