import { api } from "@/api";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export async function downloadFile(url: string) {
  if (!url || typeof url !== "string") {
    throw new Error("URL không hợp lệ");
  }

  try {
    const res = await api.post("/download", { fileUrl: url }, {
      responseType: "blob",
      headers: { "Content-Type": "application/json" },
    });

    if (res.status === 200) {
      const data = res.data;
      if (data.status !== "success") {
        throw new Error(data.message || "Tải file thất bại");
      }
    }

    const blob = new Blob([res.data], { type: res.headers["content-type"] });
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(blob);
    link.download = url.split("/").pop() || "downloaded_file";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(link.href);

    return res.data;
  } catch (err: any) {
    const message = err?.response?.data?.message || err.message || "Tải file thất bại";
    throw new Error(message);
  }
}
