import { create } from 'zustand';

interface LoginDialogState {
  isOpen: boolean;
  message: string;
  showLoginDialog: (message?: string) => void;
  hideLoginDialog: () => void;
}

export const useLoginDialogStore = create<LoginDialogState>((set) => ({
  isOpen: false,
  message: 'Bạn cần đăng nhập để tải xuống bài hát',
  showLoginDialog: (message?: string) => set({ 
    isOpen: true, 
    message: message || 'Bạn cần đăng nhập để tải xuống bài hát' 
  }),
  hideLoginDialog: () => set({ isOpen: false }),
}));
