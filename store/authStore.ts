import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// User interface based on API response
export interface User {
  id: number
  email: string
  name: string
  roleLst: string[]
  tier: 'FREE' | 'PRO' | 'BUSINESS'
  createdAt: string
  updatedAt: string
  tierExpiry: string | null
  slug: string
  createdBy: number | null
  updatedBy: number | null
  password: string
  status: 'active' | 'inactive' | 'pending'
  isActive: boolean
}

// Auth state interface
interface AuthState {
  // User data
  user: User | null
  
  // Tokens
  accessToken: string | null
  refreshToken: string | null
  accessTokenExpiresAt: number | null
  refreshTokenExpiresAt: number | null
  
  // Loading states
  isLoading: boolean
  isAuthenticated: boolean
  
  // Actions
  setAuth: (authData: {
    access_token: string
    refresh_token: string
    access_token_expires_at: number
    refresh_token_expires_at: number
    user: User
  }) => void
  
  clearAuth: () => void
  setLoading: (loading: boolean) => void
  updateUser: (user: Partial<User>) => void
  
  // Computed
  isAdmin: () => boolean
  hasRole: (role: string) => boolean
  isTokenExpired: () => boolean
}

// Create the auth store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      accessToken: null,
      refreshToken: null,
      accessTokenExpiresAt: null,
      refreshTokenExpiresAt: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      setAuth: (authData) => {
        set({
          user: authData.user,
          accessToken: authData.access_token,
          refreshToken: authData.refresh_token,
          accessTokenExpiresAt: authData.access_token_expires_at,
          refreshTokenExpiresAt: authData.refresh_token_expires_at,
          isAuthenticated: true,
          isLoading: false,
        })
      },

      clearAuth: () => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          accessTokenExpiresAt: null,
          refreshTokenExpiresAt: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      updateUser: (userData) => {
        const currentUser = get().user
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData }
          })
        }
      },

      // Computed functions
      isAdmin: () => {
        const user = get().user
        return user?.roleLst?.includes('admin') || false
      },

      hasRole: (role) => {
        const user = get().user
        return user?.roleLst?.includes(role) || false
      },

      isTokenExpired: () => {
        const expiresAt = get().accessTokenExpiresAt
        if (!expiresAt) return true
        return Date.now() / 1000 > expiresAt
      },
    }),
    {
      name: 'auth-storage', // localStorage key
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        accessTokenExpiresAt: state.accessTokenExpiresAt,
        refreshTokenExpiresAt: state.refreshTokenExpiresAt,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// Selectors for easier usage
export const useUser = () => useAuthStore((state) => state.user)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useIsLoading = () => useAuthStore((state) => state.isLoading)
export const useAccessToken = () => useAuthStore((state) => state.accessToken)
export const useIsAdmin = () => useAuthStore((state) => state.isAdmin())

// Auth actions - separate selectors to avoid infinite loop
export const useSetAuth = () => useAuthStore((state) => state.setAuth)
export const useClearAuth = () => useAuthStore((state) => state.clearAuth)
export const useSetLoading = () => useAuthStore((state) => state.setLoading)
export const useUpdateUser = () => useAuthStore((state) => state.updateUser)

// Combined actions hook with useMemo to prevent infinite loop
export const useAuthActions = () => {
  const setAuth = useSetAuth()
  const clearAuth = useClearAuth()
  const setLoading = useSetLoading()
  const updateUser = useUpdateUser()

  return { setAuth, clearAuth, setLoading, updateUser }
}
