import Link from 'next/link'
import React from 'react'

export default function Footer() {
    return (
        <>
            <footer className="mt-16 border-t border-gray-200 pt-8 pb-8">
                <div className="w-full mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div className="md:col-span-2">
                            <Link href="/" className="inline-block">
                                <img
                                    src="/logo/vncmblack.png"
                                    alt="VNCM Logo"
                                    className="h-20 w-auto object-cover"
                                />
                            </Link>
                            <p className="text-gray-600 text-sm leading-relaxed mb-4">
                                VNCM - Kho nhạc đa dạng với hàng ngàn bài hát bản quyền và không bản quyền chất lượng cao.
                                Tải nhạc miễn phí, nghe nhạc online, khám phá âm nhạc Việt Nam và quốc tế.
                            </p>
                            <div className="flex space-x-6">
                                <a href="https://www.facebook.com/vncmnetwork" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-orange-500 transition-all duration-300 transform hover:scale-110">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                    </svg>
                                </a>
                                <a href="https://www.youtube.com/@vncm555" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-orange-500 transition-all duration-300 transform hover:scale-110">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                                    </svg>
                                </a>
                                <a href="https://discord.com/invite/dtSw4GYH2e" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-orange-500 transition-all duration-300 transform hover:scale-110">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z" />
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <div>
                            <h3 className="text-black font-semibold mb-4">Liên kết nhanh</h3>
                            <ul className="space-y-2">
                                <li>
                                    <Link href="/" className="text-gray-600 hover:text-orange-500 transition-colors text-sm">
                                        Trang chủ
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/bang-gia" className="text-gray-600 hover:text-orange-500 transition-colors text-sm">
                                        Bảng giá
                                    </Link>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 className="text-black font-semibold mb-4">Chính sách</h3>
                            <ul className="space-y-2">
                                <li>
                                    <Link href="/chinh-sach-bao-mat" className="text-gray-600 hover:text-orange-500 transition-colors text-sm">
                                        Chính sách bảo mật
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/dieu-khoan-dich-vu" className="text-gray-600 hover:text-orange-500 transition-colors text-sm">
                                        Điều khoản dịch vụ
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/chinh-sach-hoan-tien" className="text-gray-600 hover:text-orange-500 transition-colors text-sm">
                                        Chính sách hoàn tiền
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/hinh-thuc-thanh-toan" className="text-gray-600 hover:text-orange-500 transition-colors text-sm">
                                        Hình thức thanh toán
                                    </Link>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div className="border-t border-gray-200 mt-8 pt-6 text-center">
                        <p className="text-gray-500 text-sm">
                            © 2025 VNCM. Tất cả quyền được bảo lưu.
                        </p>
                    </div>
                </div>
            </footer>
        </>
    )
}
