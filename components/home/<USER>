
'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { apiGetSongs, Song } from '@/api/apiSong';
import { Button } from '@/components/ui/button';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { LogoLoading } from '@/components/ui/logo-loading';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import SongListItem from './SongListItem';
import { useUrlParams } from '@/hooks/useUrlParams';
import {
  Music,
} from 'lucide-react';
import { toast } from 'sonner';
import SongListSkeleton from './SongListSkeleton';
import { apiGetGenres, Genre } from '@/api/apiGenre';
import { apiGetMoods, Mood } from '@/api/apiMood';
import { apiGetArtists, Artist } from '@/api/apiArtist';

function HomePageContent() {
  const [songs, setSongs] = useState<Song[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalSongs, setTotalSongs] = useState(0);

  const [genres, setGenres] = useState<Genre[]>([]);
  const [moods, setMoods] = useState<Mood[]>([]);
  const [artists, setArtists] = useState<Artist[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);


  const {
    page: currentPage,
    genre: selectedGenre,
    mood: selectedMood,
    artist: selectedArtist,
    search: searchQuery,
    tiger: selectedTiger,
    limit: pageSize,
    updatePage,
    updateGenre,
    updateMood,
    updateArtist,
    updateTiger,
    updateSearch,
  } = useUrlParams();

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    const loadOptions = async () => {
      try {
        setLoadingOptions(true);
        const [genresResponse, moodsResponse, artistsResponse] = await Promise.all([
          apiGetGenres({ limit: 100 }),
          apiGetMoods({ limit: 100 }),
          apiGetArtists({ limit: 100 })
        ]);
        setGenres(genresResponse.items);
        setMoods(moodsResponse.items);
        setArtists(artistsResponse.items);
      } catch (error) {
        console.error('Error loading options:', error);
        toast.error('Không thể tải danh sách thể loại và mood');
      } finally {
        setLoadingOptions(false);
      }
    };

    loadOptions();
  }, []);

  const fetchSongs = async (page: number, search: string = '') => {
    try {
      if (!songs || songs.length === 0) {
        setLoading(true);
      }
      setError(null);

      const response = await apiGetSongs({
        page,
        limit: pageSize,
        ...(search.trim() && { search: search.trim() }),
        ...(selectedGenre && { genreSlug: selectedGenre }),
        ...(selectedMood && { moodSlug: selectedMood }),
        ...(selectedArtist && { artistSlug: selectedArtist }),
        ...(selectedTiger && { tier: selectedTiger }),
      });

      setSongs(response.items || []);
      setTotalSongs(response.total || 0);
    } catch (err) {
      console.error('Error fetching songs:', err);
      setError('Không thể tải danh sách bài hát');
      toast.error('Không thể tải danh sách bài hát');
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    fetchSongs(currentPage, searchQuery);
  }, [currentPage, selectedGenre, selectedMood, selectedArtist, selectedTiger, pageSize, searchQuery]);

  if (!isHydrated) {
    return <LogoLoading />;
  }

  if (loading && (!songs || songs.length === 0)) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="py-16">
            <LogoLoading
              message="Đang tải danh sách bài hát..."
              size="lg"
            />
          </div>
        </div>
      </div>
    );
  }

  if (error && songs.length === 0) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <Music className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Không thể tải bài hát</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => fetchSongs(currentPage, searchQuery)} className="bg-black hover:bg-gray-800 text-white">
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto">
        <div className="mb-4">
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row sm:flex-wrap items-start sm:items-center gap-2 sm:gap-3 lg:gap-4">
              <div className="flex flex-wrap items-center gap-4 w-full">
                {/* Mobile filter button */}
                <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
                  <SheetTrigger asChild>
                    <Button
                      variant="ghost"
                      className="md:hidden"
                    >
                      <svg width="40" height="39" viewBox="0 0 40 39" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M30 11.5H22.2222M17.7778 11.5H10M30 19.5H20M15.5556 19.5H10M30 27.5H24.4444M20 27.5H10M22.2222 9.5V13.5M15.5556 17.5V21.5M24.4444 25.5V29.5" stroke="black" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="bottom" className="h-[70vh]">
                    <SheetHeader>
                      <SheetTitle>Bộ lọc tìm kiếm</SheetTitle>
                      <SheetDescription>
                        Tìm kiếm và lọc bài hát theo ý muốn
                      </SheetDescription>
                    </SheetHeader>
                    <div className="mt-6 space-y-4">
                      {/* Search Input */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                          Tìm kiếm bài hát
                        </label>
                        <input
                          type="text"
                          placeholder="Nhập tên bài hát..."
                          value={searchQuery}
                          onChange={(e) => {
                            updateSearch(e.target.value);
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      {/* Genre Filter */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                          Thể loại
                        </label>
                        <SearchableSelect
                          options={genres.map(genre => genre.name)}
                          value={genres.find(g => g.slug === selectedGenre)?.name || ''}
                          onValueChange={(name) => {
                            const genre = genres.find(g => g.name === name);
                            updateGenre(genre?.slug || '');
                          }}
                          placeholder="Chọn thể loại"
                          searchPlaceholder="Tìm thể loại..."
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                          Tâm trạng
                        </label>
                        <SearchableSelect
                          options={moods.map(mood => mood.name)}
                          value={moods.find(m => m.slug === selectedMood)?.name || ''}
                          onValueChange={(name) => {
                            const mood = moods.find(m => m.name === name);
                            updateMood(mood?.slug || '');
                          }}
                          placeholder="Chọn tâm trạng"
                          searchPlaceholder="Tìm tâm trạng..."
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                          Nghệ sĩ
                        </label>
                        <SearchableSelect
                          options={artists.map(artist => artist.name)}
                          value={artists.find(a => a.slug === selectedArtist)?.name || ''}
                          onValueChange={(name) => {
                            const artist = artists.find(a => a.name === name);
                            updateArtist(artist?.slug || '');
                          }}
                          placeholder="Chọn nghệ sĩ"
                          searchPlaceholder="Tìm nghệ sĩ..."
                        />
                      </div>

                      <div className="pt-4">
                        <Button
                          variant="outline"
                          onClick={() => {
                            updateGenre('');
                            updateMood('');
                            updateArtist('');
                            updateTiger('');
                            updateSearch('');
                            setIsFilterSheetOpen(false);
                          }}
                          className="w-full"
                        >
                          Xóa tất cả bộ lọc
                        </Button>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>

                <div className="w-48 hidden md:block">
                  <SearchableSelect
                    options={['Tất cả', ...genres.map(genre => genre.name.trim())]}
                    value={selectedGenre ? genres.find(g => g.slug === selectedGenre)?.name.trim() || '' : ''}
                    onValueChange={(value) => {
                      if (value === 'Tất cả' || value === '') {
                        updateGenre('');
                      } else {
                        const genre = genres.find(g => g.name.trim() === value.trim());
                        if (genre) {
                          updateGenre(genre.slug);
                        }
                      }
                    }}
                    placeholder="Tìm dòng nhạc"
                    searchPlaceholder="Tìm dòng nhạc"
                    disabled={loadingOptions}
                  />
                </div>

                <div className="w-48 hidden md:block">
                  <SearchableSelect
                    options={['Tất cả', ...moods.map(mood => mood.name.trim())]}
                    value={selectedMood ? moods.find(m => m.slug === selectedMood)?.name.trim() || '' : ''}
                    onValueChange={(value) => {
                      if (value === 'Tất cả' || value === '') {
                        updateMood('');
                      } else {
                        const mood = moods.find(m => m.name.trim() === value.trim());
                        if (mood) {
                          updateMood(mood.slug);
                        }
                      }
                    }}
                    placeholder="Tìm tâm trạng"
                    searchPlaceholder="Tìm tâm trạng"
                    disabled={loadingOptions}
                  />
                </div>

                <div className="w-48 hidden md:block">
                  <SearchableSelect
                    options={['Tất cả', ...artists.map(artist => artist.name.trim())]}
                    value={selectedArtist && artists.length > 0 ? artists.find(a => a.slug === selectedArtist)?.name.trim() || '' : ''}
                    onValueChange={(value) => {
                      if (value === 'Tất cả' || value === '') {
                        updateArtist('');
                      } else {
                        const artist = artists.find(a => a.name.trim() === value.trim());
                        if (artist) {
                          updateArtist(artist.slug);
                        }
                      }
                    }}
                    placeholder="Tìm nghệ sĩ"
                    searchPlaceholder="Tìm nghệ sĩ"
                    disabled={loadingOptions}
                  />
                </div>

                <div className="w-48 hidden md:block">
                  <SearchableSelect
                    options={['Tất cả', 'FREE', 'BUSINESS', 'PRO']}
                    value={selectedTiger ? selectedTiger : ''}
                    onValueChange={(value) => {
                      if (value === 'Tất cả' || value === '') {
                        updateTiger('');
                      } else {
                        updateTiger(value);
                      }
                    }}
                    placeholder="Tìm gói đăng ký"
                    searchPlaceholder="Tìm gói"
                  />
                </div>
              </div>
            </div>


          </div>
        </div>
        <div className="bg-white overflow-hidden">
          {loading ? (
            <SongListSkeleton />
          ) : !songs || songs.length === 0 ? (
            <div className="text-center py-12">
              <Music className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy bài hát</h3>
              <p className="text-gray-500">Thử điều chỉnh tìm kiếm hoặc bộ lọc của bạn</p>
            </div>
          ) : (
            <>
              {songs?.map((song) => (
                <SongListItem key={song.id} song={song} songs={songs || []} />
              ))}
            </>
          )}
        </div>

        {totalSongs > pageSize && (
          <div className="mt-6 mb-6 flex items-center justify-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (currentPage > 1) {
                  updatePage(currentPage - 1);
                }
              }}
              disabled={currentPage === 1}
              className="flex items-center gap-2"
            >
              <span>←</span>
            </Button>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {currentPage}/{Math.ceil(totalSongs / pageSize)}
              </span>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (currentPage < Math.ceil(totalSongs / pageSize)) {
                  updatePage(currentPage + 1);
                }
              }}
              disabled={currentPage >= Math.ceil(totalSongs / pageSize)}
              className="flex items-center gap-2"
            >
              <span>→</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export default function HomePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="py-16">
            <LogoLoading
              message="Đang tải trang..."
              size="lg"
            />
          </div>
        </div>
      </div>
    }>
      <HomePageContent />
    </Suspense>
  );
}
