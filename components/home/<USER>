'use client';

import dynamic from 'next/dynamic';
import { LogoLoading } from '@/components/ui/logo-loading';

const HomePageContent = dynamic(() => import('./seaction-home'), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="py-16">
          <LogoLoading
            message="Đang tải trang..."
            size="lg"
          />
        </div>
      </div>
    </div>
  )
});

export default function HomePageClient() {
  return <HomePageContent />;
}
