import React, { useCallback, useState } from 'react';
import { Song } from '@/api/apiSong';
import { Button } from '@/components/ui/button';

import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import { useMusicPlayer } from '@/contexts/MusicPlayerContext';
import { useUser, useIsAuthenticated } from '@/store/authStore';
import { useLoginDialogStore } from '@/store/loginDialogStore';
import {
  Music,
  Play,
  Pause,
  Download,
  Clock,

  Heart,
  X,
  ChevronUp
} from 'lucide-react';
import { toast } from 'sonner';
import { downloadFile } from '@/lib/utils';
import { Badge } from '../ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../ui/collapsible';

interface SongListItemProps {
  song: Song;
  songs: Song[];
}

// Download Format Dialog Component
const DownloadFormatDialog = ({ song, open, onOpenChange }: { song: Song; open: boolean; onOpenChange: (open: boolean) => void }) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async (format: 'mp3' | 'wav') => {
    setIsDownloading(true);
    try {
      await downloadFile(format === 'mp3' ? song.mp3Url : song.fileUrl);
      toast.success(`Tải xuống ${format.toUpperCase()} thành công!`);
      onOpenChange(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Tải xuống thất bại');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg p-0 overflow-hidden bg-white rounded-2xl shadow-2xl border-0">
        <button
          onClick={() => onOpenChange(false)}
          className="absolute top-4 right-4 z-10 w-8 h-8 rounded-full bg-black/10 hover:bg-black/20 flex items-center justify-center transition-colors"
        >
          <X className="w-4 h-4 text-gray-600" />
        </button>

        <div className="relative px-8 pt-8 pb-6">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-violet-500 via-purple-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
              <Download className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Chọn định dạng</h2>
            <p className="text-gray-500 text-sm">Tải xuống bài hát với định dạng phù hợp</p>
          </div>
        </div>

        {/* Song Preview */}
        <div className="mx-8 mb-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
          <div className="flex items-center gap-4">
            <div className="w-14 h-14 rounded-xl overflow-hidden bg-gradient-to-br from-orange-400 to-pink-500 flex items-center justify-center flex-shrink-0 shadow-md">
              {song.imageUrl ? (
                <img
                  src={song.imageUrl}
                  alt={song.songName}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              ) : (
                <Music className="w-7 h-7 text-white" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate text-base">{song.songName}</h3>
              <p className="text-gray-600 truncate text-sm">{song.artistName}</p>
            </div>
          </div>
        </div>

        <div className="px-8 pb-8 space-y-4">
          <div
            onClick={() => !isDownloading && song.mp3Url && handleDownload('mp3')}
            className={`group relative p-5 rounded-2xl border-2 transition-all duration-300 cursor-pointer ${
              !song.mp3Url || isDownloading
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
                : 'border-orange-200 bg-white hover:border-orange-300 hover:shadow-lg hover:shadow-orange-100 hover:-translate-y-1'
            }`}
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                <span className="text-white font-bold text-sm">MP3</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold text-gray-900">MP3 Audio</h4>
                  <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs font-medium rounded-full">
                    Phổ biến
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {isDownloading ? 'Đang tải xuống...' : 'Kích thước nhỏ, tương thích cao với mọi thiết bị'}
                </p>
              </div>
              <div className="text-orange-500">
                <Download className="w-5 h-5" />
              </div>
            </div>
          </div>

          <div
            onClick={() => !isDownloading && song.fileUrl && handleDownload('wav')}
            className={`group relative p-5 rounded-2xl border-2 transition-all duration-300 cursor-pointer ${
              !song.fileUrl || isDownloading
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
                : 'border-blue-200 bg-white hover:border-blue-300 hover:shadow-lg hover:shadow-blue-100 hover:-translate-y-1'
            }`}
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                <span className="text-white font-bold text-sm">WAV</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold text-gray-900">WAV Audio</h4>
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                    Chất lượng cao
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {isDownloading ? 'Đang tải xuống...' : 'Chất lượng gốc, không nén, dành cho chuyên nghiệp'}
                </p>
              </div>
              <div className="text-blue-500">
                <Download className="w-5 h-5" />
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-8 pb-6">
          <div className="text-center p-3 bg-gray-50 rounded-xl">
            <p className="text-xs text-gray-500">
              📁 Tên file: <span className="font-medium">{song.songName} - {song.artistName}</span>
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const SongListItem = React.memo(({ song, songs }: SongListItemProps) => {
  const [showFormatDialog, setShowFormatDialog] = useState(false);
  const { playSong, currentSong, isPlaying, togglePlayPause } = useMusicPlayer();
  const user = useUser();
  const isAuthenticated = useIsAuthenticated();
  const { showLoginDialog } = useLoginDialogStore();

  const formatDuration = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const getTierBadge = useCallback((tier: string) => {
    switch (tier) {
      case 'PRO':
        return (
          <span className="bg-green-400 text-black px-3 py-1 rounded-full text-xs font-medium">
            ★ PRO
          </span>
        );
      case 'BUSINESS':
        return (
          <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
            ★ BUSINESS
          </span>
        );
      default:
        return (
          <span className="bg-black text-white px-3 py-1 rounded-full text-xs font-medium">
            ★ MIỄN PHÍ
          </span>
        );
    }
  }, []);

  const getTierString = useCallback((tier: any): string => {
    if (typeof tier === 'string') return tier;
    if (typeof tier === 'number') {
      switch (tier) {
        case 1: return 'FREE';
        case 2: return 'PRO';
        case 3: return 'BUSINESS';
        default: return 'FREE';
      }
    }
    return 'FREE';
  }, []);

  const canDownload = useCallback(() => {
    if (!isAuthenticated || !user || user.tier === undefined || user.tier === null) return true;

    const userTier = getTierString(user.tier);
    const songTier = getTierString(song.tier);
    const tierHierarchy: { [key: string]: number } = { 'FREE': 1, 'PRO': 2, 'BUSINESS': 3 };

    return tierHierarchy[userTier] >= tierHierarchy[songTier];
  }, [isAuthenticated, user, song.tier, getTierString]);

  const handlePlaySong = useCallback(() => {
    const isCurrentSong = currentSong?.id === song.id;
    if (isCurrentSong) {
      togglePlayPause();
    } else {
      playSong(song, songs);
    }
  }, [currentSong?.id, song.id, song, songs, togglePlayPause, playSong]);

  const handleDownload = useCallback(() => {
    if (!isAuthenticated) {
      showLoginDialog('Bạn cần đăng nhập để tải xuống bài hát');
      return;
    }

    if (!canDownload()) {
      const songTierString = getTierString(song.tier);
      toast.error(`Bạn cần nâng cấp lên gói ${songTierString} để tải xuống bài hát này`);
      return;
    }

    setShowFormatDialog(true);
  }, [isAuthenticated, canDownload, song, showLoginDialog, getTierString]);

  const isCurrentSong = currentSong?.id === song.id;

  return (
    <>
      {/* Mobile Layout */}
      <div className={`md:hidden transition-colors border-b border-gray-100 last:border-b-0 ${
        isCurrentSong && isPlaying
          ? 'bg-[#B1FF0A] hover:bg-[#A0E609]'
          : 'hover:bg-gray-50'
      }`}>
        <div className="flex items-center gap-3 p-4">
          {/* Play Button */}
          <Button
            size="sm"
            onClick={handlePlaySong}
            className="w-10 h-10 p-0 rounded-full bg-transparent hover:bg-gray-100 border-0 shadow-none flex-shrink-0"
          >
            {isCurrentSong && isPlaying ? (
              <Pause className="w-5 h-5 text-black" />
            ) : (
              <Play className="w-5 h-5 text-black" />
            )}
          </Button>

          {/* Song Image */}
          <div className="w-14 h-14 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
            {song.imageUrl ? (
              <img
                src={song.imageUrl}
                alt={song.songName}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <Music className="w-7 h-7 text-white" />
            )}
          </div>

          {/* Song Info */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-black truncate text-base leading-tight mb-1">
              {song.songName}
            </h3>
            <p className="text-gray-500 text-sm truncate uppercase font-medium mb-2">
              {song.artistName}
            </p>

            {/* Mobile Meta Info */}
            <div className="flex items-center gap-3 text-xs text-gray-500">
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatDuration(song.duration)}
              </span>
              <span>{song.tempo} BPM</span>
              <span>{song.pitch}</span>
            </div>
          </div>

          {/* Mobile Actions */}
          <div className="flex flex-col items-center gap-2 flex-shrink-0">
            {getTierBadge(song.tier)}
            <div className="flex items-center gap-1">
              <Button size="sm" variant="ghost" className="w-8 h-8 p-0 hover:bg-gray-200 rounded-full">
                <Heart className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="w-8 h-8 p-0 hover:bg-gray-200 rounded-full"
                onClick={handleDownload}
                title={
                  !isAuthenticated
                    ? 'Đăng nhập để tải xuống'
                    : !canDownload()
                      ? `Cần gói ${getTierString(song.tier)} để tải xuống`
                      : 'Tải xuống bài hát'
                }
              >
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Tags Row */}
        <div className="px-4 pb-4 flex items-center gap-2 flex-wrap">
          <CollapsibleBadges
            items={song.genreLst || []}
            maxDisplay={2}
            variant="outline"
          />
          <CollapsibleBadges
            items={song.moodLst || []}
            maxDisplay={2}
            variant="default"
          />
        </div>
      </div>

      {/* Desktop Layout */}
      <div className={`hidden md:flex items-center gap-4 py-2 px-4 transition-colors border-b border-gray-100 last:border-b-0 ${
        isCurrentSong && isPlaying
          ? 'bg-[#B1FF0A] hover:bg-[#A0E609]'
          : 'hover:bg-gray-50'
      }`}>
        <div className="w-8 flex justify-center">
          <Button
            size="sm"
            onClick={handlePlaySong}
            className="w-6 h-6 p-0 rounded-full bg-transparent hover:bg-gray-100 border-0 shadow-none"
          >
            {isCurrentSong && isPlaying ? (
              <Pause className="w-4 h-4 text-black" />
            ) : (
              <Play className="w-4 h-4 text-black" />
            )}
          </Button>
        </div>

        <div className="w-16 flex justify-center">
          <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
            {song.imageUrl ? (
              <img
                src={song.imageUrl}
                alt={song.songName}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <Music className="w-6 h-6 text-white" />
            )}
          </div>
        </div>

        <div className="flex-1 min-w-0 pr-4">
          <h3 className="font-semibold text-black truncate text-base leading-tight">
            {song.songName}
          </h3>
          <p className="text-gray-500 text-sm truncate uppercase font-medium">
            {song.artistName}
          </p>
        </div>

        <div className="w-16 flex items-center justify-center gap-1 text-gray-600 text-sm font-medium">
          <Clock className="w-4 h-4" />
          <span>{formatDuration(song.duration)}</span>
        </div>

        <div className="w-20 text-gray-600 text-sm font-medium text-center">
          <div>{song.tempo} BPM</div>
          <div className="text-xs text-gray-500">{song.pitch}</div>
        </div>

        <div className="w-28 text-gray-600 text-sm text-start flex justify-center">
          <CollapsibleBadges
              items={song.genreLst || []}
              maxDisplay={1}
              variant="outline"
            />
        </div>

        <div className="w-28 text-gray-600 text-sm text-start flex justify-center">
          <CollapsibleBadges
              items={song.moodLst || []}
              maxDisplay={1}
              variant="default"
            />
        </div>

        <div className="w-28 flex justify-center">
          {getTierBadge(song.tier)}
        </div>

        <div className="w-36 flex items-center justify-center gap-1">
          <Button size="sm" variant="ghost" className="w-8 h-8 p-0 hover:bg-gray-200 rounded-full">
            <span className="text-lg font-normal">+</span>
          </Button>
          <Button size="sm" variant="ghost" className="w-8 h-8 p-0 hover:bg-gray-200 rounded-full">
            <Heart className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="ghost" className="w-8 h-8 p-0 hover:bg-gray-200 rounded-full">
            <span className="text-base">🔗</span>
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="w-8 h-8 p-0 hover:bg-gray-200 rounded-full"
            onClick={handleDownload}
            title={
              !isAuthenticated
                ? 'Đăng nhập để tải xuống'
                : !canDownload()
                  ? `Cần gói ${getTierString(song.tier)} để tải xuống`
                  : 'Tải xuống bài hát'
            }
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <DownloadFormatDialog
        song={song}
        open={showFormatDialog}
        onOpenChange={setShowFormatDialog}
      />
    </>
  );
});

SongListItem.displayName = 'SongListItem';

export default SongListItem;


const CollapsibleBadges = ({
  items,
  maxDisplay = 1,
  getColor,
  variant = "outline"
}: {
  items: Array<{ id: number; name: string }>;
  maxDisplay?: number;
  getColor?: (name: string) => string;
  variant?: "outline" | "default";
}) => {
  const [isOpen, setIsOpen] = useState(false);

  if (items.length === 0) {
    return <span className="text-gray-400 text-xs inline-block">-</span>;
  }

  if (items.length <= maxDisplay) {
    return (
      <div className="flex flex-wrap gap-1">
        {items.map((item) => (
          <Badge
            key={item.id}
            variant={variant}
            className={`text-xs border-none ${getColor ? getColor(item.name) : ''}`}
          >
            {item.name}
          </Badge>
        ))}
      </div>
    );
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <div className="flex flex-wrap gap-1">
        {items.slice(0, maxDisplay).map((item) => (
          <Badge
            key={item.id}
            variant={variant}
            className={`text-xs border-none ${getColor ? getColor(item.name) : ''}`}
          >
            {item.name}
          </Badge>
        ))}
        {!isOpen && (
          <CollapsibleTrigger asChild>
            <Badge
              variant="outline"
              className="text-xs border-none bg-gray-100 text-gray-600 cursor-pointer hover:bg-gray-200 transition-colors"
            >
              +{items.length - maxDisplay}
            </Badge>
          </CollapsibleTrigger>
        )}
      </div>
      <CollapsibleContent className="mt-1">
        <div className="flex flex-wrap gap-1">
          {items.slice(maxDisplay).map((item) => (
            <Badge
              key={item.id}
              variant={variant}
              className={`text-xs border-none ${getColor ? getColor(item.name) : ''}`}
            >
              {item.name}
            </Badge>
          ))}
          <CollapsibleTrigger asChild>
            <Badge
              variant="outline"
              className="text-xs border-none bg-gray-100 text-gray-600 cursor-pointer hover:bg-gray-200 transition-colors flex items-center"
            >
              <ChevronUp className="h-3 w-3" />
            </Badge>
          </CollapsibleTrigger>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};