"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Menu, LogOut, Search, X, User } from "lucide-react"
import { Sidebar } from "@/components/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useUser, useIsAuthenticated } from "@/store/authStore"
import { userLogout } from "@/api"
import { Toaster } from "@/components/ui/sonner"
import { MusicPlayerProvider } from "@/contexts/MusicPlayerContext"
import { MusicPlayer } from "@/components/MusicPlayer"
import { GlobalLoginDialog } from "@/components/global-login-dialog"
import Footer from "./footer"

interface ClientLayoutProps {
  children: React.ReactNode
}

export function ClientLayout({ children }: ClientLayoutProps) {
  const [sidebarO<PERSON>, setSidebarOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const router = useRouter()
  const user = useUser()
  const isAuthenticated = useIsAuthenticated()

  const handleLogout = async () => {
    try {
      await userLogout()
      router.push('/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/?search=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const handleResetSearch = () => {
    setSearchQuery("")
    router.push("/")
  }
  return (
    <MusicPlayerProvider>
      <div className="min-h-screen bg-white flex">
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        <div className={`
          fixed inset-y-0 left-0 z-50 w-full md:w-56 transform transition-transform duration-300 ease-in-out
          md:fixed md:translate-x-0 md:block
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <Sidebar
            isMobile={true}
            onClose={() => setSidebarOpen(false)}
          />
        </div>

        <div className="flex-1 flex flex-col min-w-0 md:ml-56">
          <div className="bg-white p-4 md:hidden">
            <div className="flex items-center justify-between w-full mb-4">
              <Link href="/" className="cursor-pointer">
                <img
                  src="/logo/vncmblack.png"
                  alt="VNCM Logo"
                  className="h-20 w-auto object-contain"
                />
              </Link>
              <div className="flex items-center gap-2">
                {isAuthenticated && user ? (
                  <>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <div className="flex items-center gap-2 px-2 py-1 rounded-full bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 transition-all cursor-pointer">
                          {/* Avatar */}
                          <div className="w-6 h-6 rounded-full overflow-hidden bg-white flex items-center justify-center">
                            <img
                              src="/avatar/anh.svg"
                              alt="User Avatar"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E";
                              }}
                            />
                          </div>

                          {/* User Info - Hidden on small screens */}
                          <div className="hidden sm:flex items-center gap-2">
                            <span className="text-black font-semibold text-xs">{user.name}</span>
                            <span className="px-1.5 py-0.5 bg-purple-500 text-white text-xs font-medium rounded-full uppercase">
                              {user.tier || 'FREE'}
                            </span>
                          </div>
                        </div>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-56">
                        <DropdownMenuLabel>
                          <div>
                            <p className="font-medium">Hello, {user.name}</p>
                            <p className="text-sm text-gray-600">{user.email}</p>
                          </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href="/profile" className="flex items-center">
                            <User className="mr-2 h-4 w-4" />
                            Thông tin cá nhân
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                          <LogOut className="mr-2 h-4 w-4" />
                          Đăng xuất
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="md:hidden text-gray-600 hover:text-black"
                      onClick={() => setSidebarOpen(true)}
                    >
                      <Menu className="h-5 w-5" />
                    </Button>
                  </>
                ) : (
                  <>
                    <Link href="/login">
                      <Button
                        size="sm"
                        variant="default"
                        className="bg-black text-white transition-colors"
                      >
                        Đăng nhập
                      </Button>
                    </Link>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="md:hidden text-gray-600 hover:text-black"
                      onClick={() => setSidebarOpen(true)}
                    >
                      <Menu className="h-5 w-5" />
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="bg-white p-4 hidden md:block">
            <div className="flex items-center justify-between w-full">
              <div className="flex-1 max-w-md">
                <form onSubmit={handleSearch} className="relative">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Tìm kiếm bài hát, nghệ sĩ..."
                      value={searchQuery}
                      onChange={handleSearchInputChange}
                      className="w-full h-12 pl-10 pr-20 py-2 border border-black rounded-full focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent text-sm"
                    />

                    {searchQuery && (
                      <Button
                        type="button"
                        onClick={handleResetSearch}
                        className="absolute right-12 top-1/2 transform -translate-y-1/2 h-6 w-6 rounded-full bg-black text-white p-4"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}

                    <Button
                      type="submit"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 h-8 w-8 rounded-full bg-black text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent p-0"
                    >
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                </form>

              </div>

              {/* User Menu */}
              <div className="flex items-center gap-2 ml-4">
                {isAuthenticated && user ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <div className="flex items-center gap-6 px-8 pt-1 pb-2 rounded-full bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 transition-all cursor-pointer">
                        {/* Avatar */}
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-white flex items-center justify-center">
                          <img
                            src="/avatar/anh.svg"
                            alt="User Avatar"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E";
                            }}
                          />
                        </div>

                        <div className="flex flex-col items-center gap-2">
                          <span className="text-black font-semibold text-sm">{user.name}</span>
                          <span className="flex items-center gap-2 px-4 py-2 bg-purple-500 text-white text-xs font-medium rounded-full uppercase">
                            <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M5 0L6.12257 3.45492H9.75528L6.81636 5.59017L7.93893 9.04508L5 6.90983L2.06107 9.04508L3.18364 5.59017L0.244718 3.45492H3.87743L5 0Z" fill="white" />
                            </svg>
                            {user.tier || 'FREE'}
                          </span>
                        </div>
                      </div>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuLabel>
                        <div>
                          <p className="font-medium">Hello, {user.name}</p>
                          <p className="text-sm text-gray-600">{user.email}</p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href="/profile" className="flex items-center">
                          <User className="mr-2 h-4 w-4" />
                          Thông tin cá nhân
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                        <LogOut className="mr-2 h-4 w-4" />
                        Đăng xuất
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <>
                    <Link href="/login">
                      <Button
                        variant="default"
                        className="bg-black text-white transition-colors px-12 py-8 rounded-full uppercase"
                      >
                        Đăng nhập
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="overflow-auto px-4">
            {children}
            <Footer />
          </div>
        </div>
        <MusicPlayer />
        <GlobalLoginDialog />
        <Toaster />
      </div>
    </MusicPlayerProvider>
  )
}
