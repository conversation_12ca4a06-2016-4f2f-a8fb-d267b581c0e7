'use client';

import { googleLogin } from '@/api/apiAuth';
import { GoogleLogin, CredentialResponse } from '@react-oauth/google';
import { useState } from 'react';
import { Button } from '@/components/ui/button';

type GoogleLoginButtonProps = {
  onLoginSuccess?: (token: string) => void;
  children?: React.ReactNode;
  className?: string;
};

const GoogleLoginButton = ({ onLoginSuccess, children, className }: GoogleLoginButtonProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleGoogleLoginSuccess = async (tokenResponse: CredentialResponse) => {
    setLoading(true);
    setError('');
    try {
      const credential = tokenResponse.credential;
      if (!credential) {
        setError('Không lấy đượ<PERSON> access token từ Google.');
        setLoading(false);
        return;
      }
      // Gửi credential về BE
      try {
        const authResponse = await googleLogin(credential);
        if (onLoginSuccess) {
          onLoginSuccess(authResponse.access_token);
        }
      }
      catch (error) {
        setError('Đăng nhập Google thất bại.');
      }
    } catch (err) {
      setError('Có lỗi xảy ra khi đăng nhập.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLoginError = () => {
    setError('Đăng nhập Google thất bại.');
  };

  // If children are provided, render custom button with overlay
  if (children) {
    return (
      <div className="flex flex-col items-center w-full">
        <div className="relative w-full">
          <GoogleLogin
            onSuccess={handleGoogleLoginSuccess}
            onError={handleGoogleLoginError}
            width="80%"
            theme="filled_black"
            shape="pill"
            size="large"
          />

          <Button
            disabled={loading}
            variant="outline"
            className={`${className} absolute inset-0 flex items-center justify-center bg-black text-white hover:bg-gray-800`}
            style={{
              pointerEvents: 'none',
              zIndex: 1
            }}
          >
            {children}
          </Button>
        </div>

        {loading && <span className="mt-2 text-blue-500">Đang đăng nhập...</span>}
        {error && <span className="mt-2 text-red-500">{error}</span>}
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center w-full">
      <GoogleLogin
        onSuccess={handleGoogleLoginSuccess}
        onError={handleGoogleLoginError}
        useOneTap
        width="100%"
      />
      {loading && <span className="mt-2 text-blue-500">Đang đăng nhập...</span>}
      {error && <span className="mt-2 text-red-500">{error}</span>}
    </div>
  );
};

export default GoogleLoginButton;