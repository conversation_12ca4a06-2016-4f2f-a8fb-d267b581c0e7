'use client';

import React from 'react';
import { LoginDialog } from '@/components/ui/login-dialog';
import { useLoginDialogStore } from '@/store/loginDialogStore';

export const GlobalLoginDialog: React.FC = () => {
  const { isOpen, message, hideLoginDialog } = useLoginDialogStore();

  return (
    <LoginDialog 
      open={isOpen}
      onOpenChange={hideLoginDialog}
      message={message}
    />
  );
};
