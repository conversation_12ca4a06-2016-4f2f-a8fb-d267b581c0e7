'use client';

import React, { useState, useEffect } from 'react';
import { DataTable } from '@/components/data-table';
import { getAdminSongColumns } from './columns';
import { EditSongDialog } from './edit-song-dialog';
import { DeleteSongDialog } from './delete-song-dialog';
import { apiGetSongs, Song } from '@/api/apiSong';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Music } from 'lucide-react';
import { CreateSongDialog } from './create-song-dialog';

export default function AdminSongTable() {
  const [songs, setSongs] = useState<Song[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editSong, setEditSong] = useState<Song | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteSong, setDeleteSong] = useState<Song | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  // Pagination and search states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalSongs, setTotalSongs] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [pageSize] = useState(10); // Fixed page size

  // Fetch songs data with pagination and search
  const fetchSongs = async (page: number = currentPage, search: string = searchQuery) => {
    try {
      setLoading(true);
      setError(null);
      const searchParam = search.trim();
      const response = await apiGetSongs({
        page,
        limit: pageSize,
        ...(searchParam && { search: searchParam }),
      });
      setSongs(response.items);
      setTotalSongs(response.total);
    } catch (err) {

      setError('Không thể tải danh sách bài hát');
      toast.error('Không thể tải danh sách bài hát');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle retry
  const handleRetry = () => {
    fetchSongs();
  };

  // Handle edit song
  const handleEditSong = (song: Song) => {
    setEditSong(song);
    setEditDialogOpen(true);
  };

  // Handle delete song
  const handleDeleteSong = (song: Song) => {
    setDeleteSong(song);
    setDeleteDialogOpen(true);
  };

  // Load data on component mount and when page/search changes
  useEffect(() => {
    fetchSongs(currentPage, searchQuery);
  }, [currentPage, searchQuery]);

  // Get columns with action handlers
  const columns = getAdminSongColumns({
    editFunction: handleEditSong,
    deleteFunction: handleDeleteSong,
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Quản lý bài hát
          </CardTitle>
          <CardDescription>
            Quản lý thư viện nhạc và thông tin bài hát trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Đang tải dữ liệu...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Music className="h-5 w-5" />
            Lỗi tải dữ liệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Thử lại
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 pb-20">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Quản lý bài hát
              </CardTitle>
            </div>
            <CreateSongDialog onSongCreated={() => fetchSongs()} />
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={songs}
            manualPagination={true}
            totalItems={totalSongs}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={handlePageChange}
            onSearchChange={handleSearch}
          />
        </CardContent>

        <EditSongDialog
          song={editSong}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={() => fetchSongs()}
        />

        <DeleteSongDialog
          song={deleteSong}
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          onSuccess={() => fetchSongs()}
        />
      </Card>
    </div>
  );
}
