'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Music, Upload, Camera, Volume2 } from 'lucide-react';
import { toast } from 'sonner';
import { Song, apiGetSongInfor, apiUpdateSong, UpdateSongRequest } from '@/api/apiSong';
import { apiGetMoods, Mood } from '@/api/apiMood';
import { apiG<PERSON><PERSON><PERSON><PERSON>, Genre } from '@/api/apiGenre';
import { apiGetArtists, Artist } from '@/api/apiArtist';
import { Progress } from '../ui/progress';
import { uploadFile } from '@/api';
import { MultiSelect } from '@/components/ui/multi-select';
import { TagsInput } from '@/components/ui/tags-input';

const editSongSchema = z.object({
  songName: z.string().min(1, 'Tên bài hát không được để trống'),
  tags: z.array(z.string()).optional(),
  artists: z.array(z.string()).min(1, 'Vui lòng chọn ít nhất một nghệ sĩ'),
  genres: z.array(z.string()).min(1, 'Vui lòng chọn ít nhất một thể loại'),
  moods: z.array(z.string()).min(1, 'Vui lòng chọn ít nhất một tâm trạng'),
  tier: z.enum(['FREE', 'PRO', 'BUSINESS']),
  tempo: z.number().min(1, 'Tempo phải lớn hơn 0'),
  duration: z.number().min(1, 'Thời lượng phải lớn hơn 0'),
  pitch: z.string(),
  fileUrl: z.string().optional(),
  mp3Url: z.string().optional(),
  imageUrl: z.string().optional(),
});

type EditSongFormData = z.infer<typeof editSongSchema>;

interface EditSongDialogProps {
  song: Song | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}



export function EditSongDialog({ song, open, onOpenChange, onSuccess }: EditSongDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const [mp3File, setMp3File] = useState<File | null>(null);
  const [wavFile, setWavFile] = useState<File | null>(null);
  const [moods, setMoods] = useState<Mood[]>([])
  const [genres, setGenres] = useState<Genre[]>([])
  const [artists, setArtists] = useState<Artist[]>([])
  const [loadingOptions, setLoadingOptions] = useState(true)

  const form = useForm<EditSongFormData>({
    resolver: zodResolver(editSongSchema),
    defaultValues: {
      songName: '',
      tags: [],
      artists: [],
      genres: [],
      moods: [],
      tier: 'FREE',
      tempo: 120,
      duration: 180,
      pitch: "",
      fileUrl: '',
      mp3Url: '',
      imageUrl: '',
    },
  });

  useEffect(() => {
    const loadOptions = async () => {
      try {
        setLoadingOptions(true);
        const [moodsResponse, genresResponse, artistsResponse] = await Promise.all([
          apiGetMoods({ limit: 100 }), 
          apiGetGenres({ limit: 100 }), 
          apiGetArtists({ limit: 100 }) 
        ]);
        setMoods(moodsResponse.items);
        setGenres(genresResponse.items);
        setArtists(artistsResponse.items);
      } catch (error) {
        toast.error('Không thể tải danh sách mood, thể loại và nghệ sĩ');
      } finally {
        setLoadingOptions(false);
      }
    };

    loadOptions();
  }, []);

  useEffect(() => {
    if (song && open) {
      const genreArray = song.genreLst ? song.genreLst.map(g => g.slug) : [];
      const moodArray = song.moodLst ? song.moodLst.map(m => m.slug) : [];
      const artistArray = song.artistLst ? song.artistLst.map(a => a.slug) : [];

      form.reset({
        songName: song.songName,
        tags: song.tags || [],
        artists: artistArray,
        genres: genreArray,
        moods: moodArray,
        tier: song.tier,
        tempo: song.tempo,
        duration: song.duration,
        pitch: song.pitch,
        fileUrl: song.fileUrl,
        mp3Url: song.mp3Url || '',
        imageUrl: song.imageUrl,
      });

      setImageFile(null);
      setImagePreview(null);
      setMp3File(null);
      setWavFile(null);
    }
  }, [song, open, form]);

  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  const renderCurrentImage = () => {
    if (!song?.imageUrl) return null;

    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">Ảnh bìa hiện tại</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col">
            <div className="relative w-full h-24 rounded-lg overflow-hidden bg-gray-50 group cursor-pointer">
              <img
                src={imagePreview || song.imageUrl}
                alt={song.songName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/placeholder-music.png";
                }}
              />
              {/* Overlay khi hover */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                  <Camera className="h-8 w-8 mx-auto mb-2" />
                  <span className="text-sm font-medium">Thay đổi ảnh</span>
                </div>
              </div>
              {/* Hidden file input */}
              <input
                type="file"
                accept="image/*"
                onChange={handleImageFileChange}
                disabled={isLoading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
          </div>
          {imageFile && (
            <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
              <Upload className="h-4 w-4 mr-2 text-orange-500" />
              <span className="truncate">Ảnh mới: {imageFile.name}</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderCurrentMp3 = () => {
    const fileName = mp3File ? mp3File.name : (song?.mp3Url ? song.mp3Url.split('/').pop() : '');
    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">File MP3 hiện tại</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            <div className="relative group cursor-pointer">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                  <Music className="h-6 w-6 text-orange-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-black truncate text-sm">{fileName || 'File MP3'}</div>
                  <div className="text-xs text-gray-500 truncate">MP3 Format</div>
                </div>
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 rounded-lg flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                  <Music className="h-8 w-8 mx-auto mb-2" />
                  <span className="text-sm font-medium">Thay đổi MP3</span>
                </div>
              </div>
              <input
                type="file"
                accept="audio/mp3,audio/mpeg,.mp3"
                onChange={handleMp3FileChange}
                disabled={isLoading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
            {mp3File && (
              <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
                <Music className="h-4 w-4 mr-2 text-orange-500" />
                <span className="truncate">File MP3 mới: {mp3File.name}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderCurrentWav = () => {
    const fileName = wavFile ? wavFile.name : (song?.fileUrl ? song.fileUrl.split('/').pop() : '');
    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">File WAV hiện tại</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            <div className="relative group cursor-pointer">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                  <Volume2 className="h-6 w-6 text-orange-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-black truncate text-sm">{fileName || 'File WAV'}</div>
                  <div className="text-xs text-gray-500 truncate">WAV Format</div>
                </div>
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 rounded-lg flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                  <Volume2 className="h-8 w-8 mx-auto mb-2" />
                  <span className="text-sm font-medium">Thay đổi WAV</span>
                </div>
              </div>
              <input
                type="file"
                accept="audio/wav,.wav"
                onChange={handleWavFileChange}
                disabled={isLoading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
            {wavFile && (
              <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
                <Volume2 className="h-4 w-4 mr-2 text-orange-500" />
                <span className="truncate">File WAV mới: {wavFile.name}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const handleImageFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Vui lòng chọn file hình ảnh hợp lệ')
        return
      }
      setImageFile(file)
      const previewUrl = URL.createObjectURL(file)
      setImagePreview(previewUrl)
    }
  }

  const handleMp3FileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!(file.type.startsWith('audio/mpeg') || file.name.toLowerCase().endsWith('.mp3'))) {
        toast.error('Vui lòng chọn file MP3 hợp lệ');
        return;
      }
      setMp3File(file);
      try {
        const songInfo = await apiGetSongInfor(file);
        form.setValue('tempo', Math.round(songInfo.tempoBpm));
        form.setValue('pitch', songInfo.averagePitchNote);
        form.setValue('duration', Math.round(songInfo.durationSeconds));
      } catch (error) {
        toast.error('Không thể lấy thông tin bài hát từ file MP3');
      }
    }
  };

  const handleWavFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!(file.type.startsWith('audio/wav') || file.name.toLowerCase().endsWith('.wav'))) {
        toast.error('Vui lòng chọn file WAV hợp lệ');
        return;
      }
      setWavFile(file);
      try {
        const songInfo = await apiGetSongInfor(file);
        form.setValue('tempo', Math.round(songInfo.tempoBpm));
        form.setValue('pitch', songInfo.averagePitchNote);
        form.setValue('duration', Math.round(songInfo.durationSeconds));
      } catch (error) {
        toast.error('Không thể lấy thông tin bài hát từ file WAV');
      }
    }
  };

  const onSubmit = async (data: EditSongFormData) => {
    if (!song) return;

    try {
      setIsLoading(true);
      setIsUploading(true);
      setUploadProgress(0);

      if (mp3File) {
        toast.info('Đang tải lên file MP3...');
        const mp3Url = await uploadFile(mp3File, (progress) => {
          setUploadProgress(progress * 0.33);
        });
        if (!mp3Url || typeof mp3Url !== 'string' || mp3Url.trim() === '') {
          throw new Error('Upload file MP3 thất bại - URL không hợp lệ');
        }
        data.mp3Url = mp3Url;
      }

      if (wavFile) {
        toast.info('Đang tải lên file WAV...');
        const fileUrl = await uploadFile(wavFile, (progress) => {
          setUploadProgress(33 + progress * 0.33);
        });
        if (!fileUrl || typeof fileUrl !== 'string' || fileUrl.trim() === '') {
          throw new Error('Upload file WAV thất bại - URL không hợp lệ');
        }
        data.fileUrl = fileUrl;
      }

      if (imageFile) {
        toast.info('Đang tải lên hình ảnh...');
        const imageUrl = await uploadFile(imageFile, (progress) => {
          setUploadProgress(66 + progress * 0.34);
        });
        if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim() === '') {
          throw new Error('Upload hình ảnh thất bại - URL không hợp lệ');
        }
        data.imageUrl = imageUrl;
      }

      setIsUploading(false);
      setUploadProgress(100);

      toast.info('Đang cập nhật thông tin bài hát...');

      const artistIds = data.artists.map(slug => {
        const artist = artists.find(a => a.slug === slug);
        return artist ? artist.id : 0;
      }).filter(id => id > 0);

      const genreIds = data.genres.map(slug => {
        const genre = genres.find(g => g.slug === slug);
        return genre ? genre.id : 0;
      }).filter(id => id > 0);

      const moodIds = data.moods.map(slug => {
        const mood = moods.find(m => m.slug === slug);
        return mood ? mood.id : 0;
      }).filter(id => id > 0);

      const updateData: UpdateSongRequest = {
        songName: data.songName,
        tags: data.tags || [],
        artistIds,
        genreIds,
        moodIds,
        tier: data.tier,
        tempo: data.tempo,
        duration: data.duration,
        pitch: data.pitch,
      };

      if (data.fileUrl) updateData.fileUrl = data.fileUrl;
      if (data.mp3Url) updateData.mp3Url = data.mp3Url;
      if (data.imageUrl) updateData.imageUrl = data.imageUrl;

      await apiUpdateSong(song.slug, updateData);
      toast.success('Cập nhật bài hát thành công!');
      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      let errorMessage = 'Có lỗi xảy ra khi cập nhật bài hát';
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.message) {
        if (Array.isArray(error.response.data.message)) {
          errorMessage = error.response.data.message.join(', ');
        } else {
          errorMessage = error.response.data.message;
        }
      }
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false);
      form.reset();
      setImageFile(null);
      setMp3File(null);
      setWavFile(null);
      setImagePreview(null);
      setUploadProgress(0);
      setIsUploading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Music className="h-5 w-5" />
            Chỉnh sửa bài hát
          </DialogTitle>
          <DialogDescription>
            Cập nhật thông tin bài hát trong hệ thống
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 items-start">
                {renderCurrentImage()}
                {renderCurrentMp3()}
                {renderCurrentWav()}
            </div>
            {isUploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Đang tải lên...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="w-full" />
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="songName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên bài hát</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập tên bài hát" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <TagsInput
                        value={field.value || []}
                        onChange={field.onChange}
                        placeholder="Nhập tags và nhấn Enter hoặc dấu phẩy"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="artists"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nghệ sĩ *</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={artists.map(artist => ({ label: artist.name, value: artist.slug }))}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Chọn nghệ sĩ"
                        searchPlaceholder="Tìm kiếm nghệ sĩ..."
                        disabled={isLoading || loadingOptions}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="genres"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Thể loại *</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={genres.map(genre => ({ label: genre.name, value: genre.slug }))}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Chọn thể loại"
                        searchPlaceholder="Tìm kiếm thể loại..."
                        disabled={isLoading || loadingOptions}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="moods"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tâm trạng *</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={moods.map(mood => ({ label: mood.name, value: mood.slug }))}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Chọn tâm trạng"
                        searchPlaceholder="Tìm kiếm tâm trạng..."
                        disabled={isLoading || loadingOptions}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="tier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gói dịch vụ</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="FREE">Miễn phí</SelectItem>
                        <SelectItem value="PRO">Pro</SelectItem>
                        <SelectItem value="BUSINESS">Business</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tempo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>BPM</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="120"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Thời lượng (giây)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="180"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="pitch"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cao độ</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      onChange={(e) => field.onChange(e.target.value)}
                      placeholder="Ví dụ: C5, D#4, F3"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                Hủy
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Cập nhật
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
