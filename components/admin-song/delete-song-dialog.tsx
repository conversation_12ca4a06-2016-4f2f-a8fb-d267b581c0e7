'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Loader2, Music, Trash2 } from 'lucide-react';
import { Song, apiDeleteSong } from '@/api/apiSong';
import { toast } from 'sonner';

interface DeleteSongDialogProps {
  song: Song | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export const DeleteSongDialog: React.FC<DeleteSongDialogProps> = ({
  song,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!song) return;

    setIsDeleting(true);
    try {
      await apiDeleteSong(song.slug);
      toast.success('Xóa bài hát thành công!');
      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      console.error('Delete error:', error);
      toast.error(error.message || 'Không thể xóa bài hát. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  if (!song) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md p-0 overflow-hidden bg-white rounded-2xl shadow-2xl border-0">
        <div className="relative px-6 pt-6 pb-4">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center shadow-lg">
              <AlertTriangle className="w-8 h-8 text-black" />
            </div>
            <DialogHeader>
              <DialogTitle className="text-xl font-bold text-gray-900 mb-2">
                Xác nhận xóa bài hát
              </DialogTitle>
              <DialogDescription className="text-gray-600">
                Bạn có chắc chắn muốn xóa bài hát này không? Hành động này không thể hoàn tác.
              </DialogDescription>
            </DialogHeader>
          </div>
        </div>

        <div className="mx-6 mb-6 p-4">
          <div className="flex items-center gap-4">
            <div className="w-14 h-14 rounded-xl overflow-hidden bg-gradient-to-br from-orange-400 to-pink-500 flex items-center justify-center flex-shrink-0 shadow-md">
              {song.imageUrl ? (
                <img
                  src={song.imageUrl}
                  alt={song.songName}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              ) : (
                <Music className="w-7 h-7 text-white" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate text-base">{song.songName}</h3>
              <p className="text-gray-600 truncate text-sm">{song.artistName}</p>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {song.genre}
                </span>
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {song.tier}
                </span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="px-6 pb-6 gap-3">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
            className="flex-1"
          >
            Hủy
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            className="flex-1 bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Đang xóa...
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4 mr-2" />
                Xóa bài hát
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
