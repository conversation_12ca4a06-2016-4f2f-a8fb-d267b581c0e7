"use client";

import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Edit, Play, Pause, Trash2, ChevronUp } from "lucide-react";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Song } from "@/api/apiSong";
import { Badge } from "@/components/ui/badge";
import { useMusicPlayer } from "@/contexts/MusicPlayerContext";
// Color helper functions
const getGenreColor = (genre: string): string => {
  const colors = [
    'bg-orange-50 text-orange-700 border-orange-200',
    'bg-blue-50 text-blue-700 border-blue-200',
    'bg-green-50 text-green-700 border-green-200',
    'bg-purple-50 text-purple-700 border-purple-200',
    'bg-pink-50 text-pink-700 border-pink-200',
    'bg-yellow-50 text-yellow-700 border-yellow-200',
    'bg-indigo-50 text-indigo-700 border-indigo-200',
    'bg-red-50 text-red-700 border-red-200',
  ];
  const index = genre.length % colors.length;
  return colors[index];
};

const getMoodColor = (mood: string): string => {
  const colors = [
    'bg-pink-50 text-pink-700 border-pink-200',
    'bg-blue-50 text-blue-700 border-blue-200',
    'bg-green-50 text-green-700 border-green-200',
    'bg-yellow-50 text-yellow-700 border-yellow-200',
    'bg-purple-50 text-purple-700 border-purple-200',
    'bg-red-50 text-red-700 border-red-200',
    'bg-indigo-50 text-indigo-700 border-indigo-200',
    'bg-gray-50 text-gray-700 border-gray-200',
  ];
  const index = mood.length % colors.length;
  return colors[index];
};
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useState } from "react";

type Props = {
  editFunction?: (song: Song) => void;
  deleteFunction?: (song: Song) => void;
};

const CollapsibleBadges = ({
  items,
  maxDisplay = 2,
  getColor,
  variant = "outline"
}: {
  items: Array<{ id: number; name: string }>;
  maxDisplay?: number;
  getColor?: (name: string) => string;
  variant?: "outline" | "default";
}) => {
  const [isOpen, setIsOpen] = useState(false);

  if (items.length === 0) {
    return <span className="text-gray-400">No items</span>;
  }

  if (items.length <= maxDisplay) {
    return (
      <div className="flex flex-wrap gap-1">
        {items.map((item) => (
          <Badge
            key={item.id}
            variant={variant}
            className={getColor ? getColor(item.name) : undefined}
          >
            {item.name}
          </Badge>
        ))}
      </div>
    );
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <div className="flex flex-wrap gap-1">
        {items.slice(0, maxDisplay).map((item) => (
          <Badge
            key={item.id}
            variant={variant}
            className={getColor ? getColor(item.name) : undefined}
          >
            {item.name}
          </Badge>
        ))}
        {!isOpen && (
          <CollapsibleTrigger asChild>
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-600 cursor-pointer hover:bg-gray-200 transition-colors"
            >
              +{items.length - maxDisplay} more
            </Badge>
          </CollapsibleTrigger>
        )}
      </div>
      <CollapsibleContent className="mt-1">
        <div className="flex flex-wrap gap-1">
          {items.slice(maxDisplay).map((item) => (
            <Badge
              key={item.id}
              variant={variant}
              className={getColor ? getColor(item.name) : undefined}
            >
              {item.name}
            </Badge>
          ))}
          <CollapsibleTrigger asChild>
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-600 cursor-pointer hover:bg-gray-200 transition-colors flex items-center"
            >
              <ChevronUp className="h-3 w-3" />
            </Badge>
          </CollapsibleTrigger>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};



export const getAdminSongColumns = (props: Props): ColumnDef<Song>[] => {
  return [
    {
      accessorKey: "songName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tên bài hát" />
      ),
      cell: ({ row }) => {
        const song = row.original;
        const { playSong, currentSong, isPlaying, togglePlayPause } = useMusicPlayer();

        const handlePlayPause = (e: React.MouseEvent) => {
          e.stopPropagation();
          const isCurrentSong = currentSong?.id === song.id;

          if (isCurrentSong) {
            togglePlayPause();
          } else {
            playSong(song, [song]);
          }
        };

        const isCurrentSong = currentSong?.id === song.id;

        return (
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0 group cursor-pointer" onClick={handlePlayPause}>
              <img
                src={row.original.imageUrl}
                alt={row.getValue("songName")}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/placeholder-music.png";
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white bg-opacity-90 rounded-full p-1.5 shadow-md">
                  {isCurrentSong && isPlaying ? (
                    <Pause className="h-3 w-3 text-gray-700" />
                  ) : (
                    <Play className="h-3 w-3 text-gray-700" />
                  )}
                </div>
              </div>
            </div>
            <div className="space-y-1 min-w-0">
              <div className="font-medium text-gray-900 truncate">{row.getValue("songName")}</div>
              <div className="text-sm text-gray-500 truncate">
                {song.artistLst && song.artistLst.length > 0
                  ? song.artistLst.map(artist => artist.name).join(', ')
                  : song.artistName || 'Unknown Artist'
                }
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "genreLst",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Thể loại" />
      ),
      cell: ({ row }) => {
        const song = row.original;
        const genres = song.genreLst || [];

        if (genres.length === 0) {
          const oldGenre = song.genre;
          if (oldGenre) {
            return (
              <Badge variant="outline" className={getGenreColor(oldGenre)}>
                {oldGenre}
              </Badge>
            );
          }
          return <span className="text-gray-400">No genre</span>;
        }

        return (
          <CollapsibleBadges
            items={genres}
            maxDisplay={2}
            getColor={getGenreColor}
            variant="outline"
          />
        );
      },
      filterFn: (row, _id, value) => {
        const song = row.original;
        const genres = song.genreLst || [];
        if (genres.length > 0) {
          return genres.some(genre => value.includes(genre.name));
        }
        return value.includes(song.genre || '');
      },
    },
    {
      accessorKey: "moodLst",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tâm trạng" />
      ),
      cell: ({ row }) => {
        const song = row.original;
        const moods = song.moodLst || [];

        if (moods.length === 0) {
          const oldMood = song.mood;
          if (oldMood) {
            return (
              <Badge variant="outline" className={getMoodColor(oldMood)}>
                {oldMood}
              </Badge>
            );
          }
          return <span className="text-gray-400">No mood</span>;
        }

        return (
          <CollapsibleBadges
            items={moods}
            maxDisplay={2}
            getColor={getMoodColor}
            variant="outline"
          />
        );
      },
      filterFn: (row, _id, value) => {
        const song = row.original;
        const moods = song.moodLst || [];
        if (moods.length > 0) {
          return moods.some(mood => value.includes(mood.name));
        }
        return value.includes(song.mood || '');
      },
    },
    {
      accessorKey: "tags",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tags" />
      ),
      cell: ({ row }) => {
        const song = row.original;
        const tags = song.tags || [];

        if (tags.length === 0) {
          return <span className="text-gray-400">No tags</span>;
        }

        return (
          <CollapsibleBadges
            items={tags.map((tag, index) => ({ id: index, name: tag }))}
            maxDisplay={2}
            variant="outline"
          />
        );
      },
      filterFn: (row, _id, value) => {
        const song = row.original;
        const tags = song.tags || [];
        return tags.some(tag => value.includes(tag));
      },
    },
    {
      accessorKey: "tier",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Gói" />
      ),
      cell: ({ row }) => {
        const tierValue = row.getValue("tier") as string;
        const getTierConfig = (tier: string) => {
          switch (tier) {
            case "FREE":
              return {
                label: "Miễn phí",
                className: "bg-gray-100 text-gray-800 border-gray-200"
              }
            case "PRO":
              return {
                label: "Pro",
                className: "bg-blue-100 text-blue-800 border-blue-200"
              }
            case "BUSINESS":
              return {
                label: "Business",
                className: "bg-orange-100 text-orange-800 border-orange-200"
              }
            default:
              return {
                label: tier,
                className: "bg-gray-100 text-gray-800 border-gray-200"
              }
          }
        }

        const config = getTierConfig(tierValue)
        return (
          <Badge variant="outline" className={config.className}>
            {config.label}
          </Badge>
        )
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "duration",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Thời lượng" />
      ),
      cell: ({ row }) => {
        const duration = row.getValue("duration") as number;
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        return (
          <span className="text-sm text-gray-600">
            {minutes}:{seconds.toString().padStart(2, '0')}
          </span>
        );
      },
    },
    {
      accessorKey: "tempo",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="BPM" />
      ),
      cell: ({ row }) => (
        <span className="text-sm text-gray-600">{row.getValue("tempo")}</span>
      ),
    },

    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const song = row.original;

        return (
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => props.editFunction?.(song)}
              className="h-8 w-8 p-0 hover:bg-orange-50 hover:text-orange-600 rounded-full"
            >
              <span className="sr-only">Chỉnh sửa bài hát</span>
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => props.deleteFunction?.(song)}
              className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 rounded-full"
            >
              <span className="sr-only">Xóa bài hát</span>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];
};
