"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { Upload, Music, Loader2, Plus, Camera, Volume2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { uploadFile } from "@/api/apiUpload"
import { apiCreateSong, apiGetSongInfor, Song } from "@/api/apiSong"
import { apiGetMoods, Mood } from "@/api/apiMood"
import { api<PERSON><PERSON><PERSON><PERSON><PERSON>, Genre } from "@/api/apiGenre"
import { apiGetArt<PERSON>, Artist } from "@/api/apiArtist"
import { MultiSelect } from '@/components/ui/multi-select'
import { TagsInput } from '@/components/ui/tags-input'

const createSongSchema = z.object({
  songName: z.string().min(1, "Tên bài hát là bắt buộc"),
  tags: z.array(z.string()).optional(),
  artists: z.array(z.string()).min(1, "Vui lòng chọn ít nhất một nghệ sĩ"),
  genres: z.array(z.string()).min(1, "Vui lòng chọn ít nhất một thể loại"),
  moods: z.array(z.string()).min(1, "Vui lòng chọn ít nhất một tâm trạng"),
  tempo: z.number().min(1, "Tempo phải lớn hơn 0").max(300, "Tempo không được vượt quá 300"),
  duration: z.number().min(1, "Thời lượng phải lớn hơn 0"),
  pitch: z.string(),
  mp3Url: z.string(),
  fileUrl: z.string(),
  tier: z.enum(["FREE", "PRO", "BUSINESS"]),
})


type CreateSongFormData = z.infer<typeof createSongSchema> & {
  imageUrl?: string;
};

interface CreateSongDialogProps {
  onSongCreated?: () => void
  song?: Song
}

export function CreateSongDialog({ onSongCreated, song }: CreateSongDialogProps) {
  const [open, setOpen] = useState(false)
  const [mp3File, setMp3File] = useState<File | null>(null)
  const [wavFile, setWavFile] = useState<File | null>(null)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [moods, setMoods] = useState<Mood[]>([])
  const [genres, setGenres] = useState<Genre[]>([])
  const [artists, setArtists] = useState<Artist[]>([])
  const [loadingOptions, setLoadingOptions] = useState(true)

  const form = useForm<CreateSongFormData>({
    resolver: zodResolver(createSongSchema),
    defaultValues: {
      songName: "",
      tags: [],
      artists: [],
      genres: [],
      moods: [],
      tempo: 120,
      duration: 0,
      pitch: "",
      tier: "FREE",
      mp3Url: "",
      fileUrl: "",
    },
  })

  useEffect(() => {
    const loadOptions = async () => {
      try {
        setLoadingOptions(true);
        const [moodsResponse, genresResponse, artistsResponse] = await Promise.all([
          apiGetMoods({ limit: 100 }),
          apiGetGenres({ limit: 100 }), 
          apiGetArtists({ limit: 100 })
        ]);
        setMoods(moodsResponse.items);
        setGenres(genresResponse.items);
        setArtists(artistsResponse.items);
      } catch (error) {
        toast.error('Không thể tải danh sách mood, thể loại và nghệ sĩ');
      } finally {
        setLoadingOptions(false);
      }
    };

    loadOptions();
  }, []);

  const handleMp3FileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!(file.type.startsWith('audio/mpeg') || file.name.toLowerCase().endsWith('.mp3'))) {
        toast.error('Vui lòng chọn file MP3 hợp lệ');
        return;
      }
      setMp3File(file);
      try {
        const songInfo = await apiGetSongInfor(file);
        form.setValue('tempo', Math.round(songInfo.tempoBpm));
        form.setValue('pitch', songInfo.averagePitchNote);
        form.setValue('duration', Math.round(songInfo.durationSeconds));
      } catch (error) {
        toast.error('Không thể lấy thông tin bài hát từ file MP3');
      }
    }
  };

  const handleWavFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!(file.type.startsWith('audio/wav') || file.name.toLowerCase().endsWith('.wav'))) {
        toast.error('Vui lòng chọn file WAV hợp lệ');
        return;
      }
      setWavFile(file);
      try {
        const songInfo = await apiGetSongInfor(file);
        form.setValue('tempo', Math.round(songInfo.tempoBpm));
        form.setValue('pitch', songInfo.averagePitchNote);
        form.setValue('duration', Math.round(songInfo.durationSeconds));
      } catch (error) {
        toast.error('Không thể lấy thông tin bài hát từ file WAV');
      }
    }
  };

  const handleImageFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Vui lòng chọn file hình ảnh hợp lệ')
        return
      }
      setImageFile(file)
      const previewUrl = URL.createObjectURL(file)
      setImagePreview(previewUrl)
    }
  }

  const onSubmit = async (data: CreateSongFormData) => {
    if (!mp3File) {
      toast.error('Vui lòng chọn file MP3');
      return;
    }
    if (!wavFile) {
      toast.error('Vui lòng chọn file WAV');
      return;
    }
    if (!imageFile) {
      toast.error('Vui lòng chọn hình ảnh bìa');
      return;
    }

    try {
      setIsUploading(true);

      toast.info('Đang tải lên file MP3...');
      const mp3Url = await uploadFile(mp3File);

      toast.info('Đang tải lên file WAV...');
      const fileUrl = await uploadFile(wavFile);

      toast.info('Đang tải lên hình ảnh...');
      const imageUrl = await uploadFile(imageFile);

      if (!mp3Url || typeof mp3Url !== 'string' || mp3Url.trim() === '') {
        throw new Error('Upload file MP3 thất bại - URL không hợp lệ');
      }
      if (!fileUrl || typeof fileUrl !== 'string' || fileUrl.trim() === '') {
        throw new Error('Upload file WAV thất bại - URL không hợp lệ');
      }
      if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim() === '') {
        throw new Error('Upload hình ảnh thất bại - URL không hợp lệ');
      }

      setIsUploading(false);
      setIsCreating(true);

      toast.info('Đang tạo bài hát...');

      const artistIds = data.artists.map(slug => {
        const artist = artists.find(a => a.slug === slug);
        return artist ? artist.id : 0;
      }).filter(id => id > 0);

      const genreIds = data.genres.map(slug => {
        const genre = genres.find(g => g.slug === slug);
        return genre ? genre.id : 0;
      }).filter(id => id > 0);

      const moodIds = data.moods.map(slug => {
        const mood = moods.find(m => m.slug === slug);
        return mood ? mood.id : 0;
      }).filter(id => id > 0);

      await apiCreateSong({
        songName: data.songName,
        tags: data.tags || [],
        artistIds,
        genreIds,
        moodIds,
        tempo: data.tempo,
        duration: data.duration,
        pitch: data.pitch,
        tier: data.tier,
        mp3Url,
        fileUrl,
        imageUrl,
      });

      toast.success('Tạo bài hát thành công!');
      form.reset({
        songName: "",
        tags: [],
        artists: [],
        genres: [],
        moods: [],
        tempo: 120,
        duration: 0,
        pitch: "",
        tier: "FREE",
        mp3Url: "",
        fileUrl: "",
      });
      setMp3File(null);
      setWavFile(null);
      setImageFile(null);
      setImagePreview(null);
      setOpen(false);
      onSongCreated?.();
    } catch (error: any) {
      let errorMessage = 'Có lỗi xảy ra khi tạo bài hát';
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.message) {
        if (Array.isArray(error.response.data.message)) {
          errorMessage = error.response.data.message.join(', ');
        } else {
          errorMessage = error.response.data.message;
        }
      }
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
      setIsCreating(false);
    }
  };

  const isLoading = isUploading || isCreating

  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  const renderCurrentImage = () => {
    if (!song?.imageUrl) return null;

    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">Ảnh bìa hiện tại</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col">
            <div className="relative w-full h-24 rounded-lg overflow-hidden bg-gray-50 group cursor-pointer">
              <img
                src={imagePreview || song.imageUrl}
                alt={song.songName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/placeholder-music.png";
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                  <Camera className="h-8 w-8 mx-auto mb-2" />
                  <span className="text-sm font-medium">Thay đổi ảnh</span>
                </div>
              </div>
              <input
                type="file"
                accept="image/*"
                onChange={handleImageFileChange}
                disabled={isLoading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
          </div>
          {imageFile && (
            <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
              <Upload className="h-4 w-4 mr-2 text-orange-500" />
              <span className="truncate">Ảnh mới: {imageFile.name}</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderCurrentMp3 = () => {
    const fileName = mp3File ? mp3File.name : (song?.mp3Url ? song.mp3Url.split('/').pop() : '');
    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">File MP3 hiện tại</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            <div className="relative group cursor-pointer">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                  <Music className="h-6 w-6 text-orange-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-black truncate text-sm">{fileName || 'File MP3'}</div>
                  <div className="text-xs text-gray-500 truncate">MP3 Format</div>
                </div>
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 rounded-lg flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                  <Music className="h-8 w-8 mx-auto mb-2" />
                  <span className="text-sm font-medium">Thay đổi MP3</span>
                </div>
              </div>
              <input
                type="file"
                accept="audio/mp3,audio/mpeg,.mp3"
                onChange={handleMp3FileChange}
                disabled={isLoading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
            {mp3File && (
              <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
                <Music className="h-4 w-4 mr-2 text-orange-500" />
                <span className="truncate">File MP3 mới: {mp3File.name}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderCurrentWav = () => {
    const fileName = wavFile ? wavFile.name : (song?.fileUrl ? song.fileUrl.split('/').pop() : '');
    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">File WAV hiện tại</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            <div className="relative group cursor-pointer">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                  <Volume2 className="h-6 w-6 text-orange-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-black truncate text-sm">{fileName || 'File WAV'}</div>
                  <div className="text-xs text-gray-500 truncate">WAV Format</div>
                </div>
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 rounded-lg flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                  <Volume2 className="h-8 w-8 mx-auto mb-2" />
                  <span className="text-sm font-medium">Thay đổi WAV</span>
                </div>
              </div>
              <input
                type="file"
                accept="audio/wav,.wav"
                onChange={handleWavFileChange}
                disabled={isLoading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
            {wavFile && (
              <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
                <Volume2 className="h-4 w-4 mr-2 text-orange-500" />
                <span className="truncate">File WAV mới: {wavFile.name}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderImageUpload = () => {
    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">Ảnh bìa *</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col">
            {imagePreview ? (
              <div className="relative w-full h-24 rounded-lg overflow-hidden bg-gray-50 group cursor-pointer">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                    <Camera className="h-8 w-8 mx-auto mb-2" />
                    <span className="text-sm font-medium">Thay đổi ảnh</span>
                  </div>
                </div>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageFileChange}
                  disabled={isLoading}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            ) : (
              <div className="relative w-full h-24 rounded-lg bg-gray-50 border-2 border-dashed border-gray-300 hover:border-orange-300 transition-colors group cursor-pointer flex items-center justify-center">
                <div className="text-center text-gray-500 group-hover:text-orange-600 transition-colors">
                  <Camera className="h-8 w-8 mx-auto mb-2" />
                  <span className="text-sm font-medium">Chọn ảnh</span>
                </div>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageFileChange}
                  disabled={isLoading}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            )}
          </div>
          {imageFile && (
            <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
              <Upload className="h-4 w-4 mr-2 text-orange-500" />
              <span className="truncate">Ảnh đã chọn: {imageFile.name}</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderMp3Upload = () => {
    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">File MP3 *</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            {mp3File ? (
              <div className="relative group cursor-pointer">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                    <Music className="h-6 w-6 text-orange-500" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-black truncate text-sm">{mp3File.name}</div>
                    <div className="text-xs text-gray-500 truncate">MP3 Format</div>
                  </div>
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 rounded-lg flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                    <Music className="h-8 w-8 mx-auto mb-2" />
                    <span className="text-sm font-medium">Thay đổi MP3</span>
                  </div>
                </div>
                <input
                  type="file"
                  accept="audio/mp3,audio/mpeg,.mp3"
                  onChange={handleMp3FileChange}
                  disabled={isLoading}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            ) : (
              <div className="relative group cursor-pointer">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-orange-300 transition-colors">
                  <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                    <Music className="h-6 w-6 text-gray-400 group-hover:text-orange-500 transition-colors" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-500 group-hover:text-orange-600 transition-colors text-sm">Chọn file MP3</div>
                    <div className="text-xs text-gray-400">MP3 Format</div>
                  </div>
                </div>
                <input
                  type="file"
                  accept="audio/mp3,audio/mpeg,.mp3"
                  onChange={handleMp3FileChange}
                  disabled={isLoading}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            )}
            {mp3File && (
              <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
                <Music className="h-4 w-4 mr-2 text-orange-500" />
                <span className="truncate">File MP3: {mp3File.name}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderWavUpload = () => {
    return (
      <div className="space-y-3 h-full flex flex-col">
        <label className="text-sm font-semibold text-black block">File WAV *</label>
        <div className="bg-white border border-gray-200 rounded-xl p-4 hover:border-gray-300 transition-colors flex-1 flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            {wavFile ? (
              <div className="relative group cursor-pointer">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                    <Volume2 className="h-6 w-6 text-orange-500" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-black truncate text-sm">{wavFile.name}</div>
                    <div className="text-xs text-gray-500 truncate">WAV Format</div>
                  </div>
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 rounded-lg flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                    <Volume2 className="h-8 w-8 mx-auto mb-2" />
                    <span className="text-sm font-medium">Thay đổi WAV</span>
                  </div>
                </div>
                <input
                  type="file"
                  accept="audio/wav,.wav"
                  onChange={handleWavFileChange}
                  disabled={isLoading}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            ) : (
              <div className="relative group cursor-pointer">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-orange-300 transition-colors">
                  <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                    <Volume2 className="h-6 w-6 text-gray-400 group-hover:text-orange-500 transition-colors" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-500 group-hover:text-orange-600 transition-colors text-sm">Chọn file WAV</div>
                    <div className="text-xs text-gray-400">WAV Format</div>
                  </div>
                </div>
                <input
                  type="file"
                  accept="audio/wav,.wav"
                  onChange={handleWavFileChange}
                  disabled={isLoading}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            )}
            {wavFile && (
              <div className="mt-3 flex items-center text-sm text-gray-700 bg-gray-50 rounded-lg p-2">
                <Volume2 className="h-4 w-4 mr-2 text-orange-500" />
                <span className="truncate">File WAV: {wavFile.name}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Tạo Bài Hát
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-orange-500">Tạo Bài Hát Mới</DialogTitle>
          <DialogDescription>
            Tải lên file âm thanh và điền thông tin để tạo bài hát mới
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 items-start">
                {song ? renderCurrentImage() : renderImageUpload()}
                {song ? renderCurrentMp3() : renderMp3Upload()}
                {song ? renderCurrentWav() : renderWavUpload()}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="songName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên Bài Hát *</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập tên bài hát" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <TagsInput
                        value={field.value || []}
                        onChange={field.onChange}
                        placeholder="Nhập tags và nhấn Enter hoặc dấu phẩy"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="artists"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nghệ sĩ *</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={artists.map(artist => ({ label: artist.name, value: artist.slug }))}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Chọn nghệ sĩ"
                        searchPlaceholder="Tìm kiếm nghệ sĩ..."
                        disabled={isLoading || loadingOptions}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="genres"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Thể Loại *</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={genres.map(genre => ({ label: genre.name, value: genre.slug }))}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Chọn thể loại"
                        searchPlaceholder="Tìm kiếm thể loại..."
                        disabled={isLoading || loadingOptions}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="moods"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tâm Trạng *</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={moods.map(mood => ({ label: mood.name, value: mood.slug }))}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Chọn tâm trạng"
                        searchPlaceholder="Tìm kiếm tâm trạng..."
                        disabled={isLoading || loadingOptions}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tempo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tempo (BPM) *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="120"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Thời Lượng (giây)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Tự động từ file âm thanh"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        disabled={isLoading}
                        readOnly
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pitch"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pitch</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        onChange={(e) => field.onChange(e.target.value)}
                        disabled={isLoading}
                        placeholder="Ví dụ: C5, D#4, F3"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gói Truy Cập *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn gói" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="FREE">FREE</SelectItem>
                        <SelectItem value="PRO">PRO</SelectItem>
                        <SelectItem value="BUSINESS">BUSINESS</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                Hủy
              </Button>
              <Button
                type="submit"
                className="bg-orange-500 hover:bg-orange-600 text-white"
                disabled={isLoading || !mp3File || !wavFile || !imageFile}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {isUploading ? 'Đang tải lên...' : 'Đang tạo...'}
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Tạo Bài Hát
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
