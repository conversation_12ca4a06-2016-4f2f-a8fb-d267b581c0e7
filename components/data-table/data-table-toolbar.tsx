import { Table } from '@tanstack/react-table'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { DataTableViewOptions } from './data-table-view-options'
import { useState, useEffect } from 'react'
import { X, Search } from 'lucide-react'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onCreateItem?: (newItem: any) => void
  onSearchChange?: (searchTerm: string) => void
  filterComponent?: React.ReactNode
  searchValue?: string
}

export function DataTableToolbar<TData>({
  table,
  onCreateItem,
  onSearchChange,
  filterComponent,
  searchValue: externalSearchValue = '',
}: DataTableToolbarProps<TData>) {
  const [searchValue, setSearchValue] = useState(externalSearchValue)
  const isFiltered = table.getState().columnFilters.length > 0

  // Sync with external search value (from URL params)
  useEffect(() => {
    setSearchValue(externalSearchValue)
  }, [externalSearchValue])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(value)
    // Only trigger search if no onSearchChange callback (local filtering)
    if (!onSearchChange) {
      table.getColumn('name')?.setFilterValue(value)
    }
  }

  const handleSearch = () => {
    if (onSearchChange) {
      onSearchChange(searchValue)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const handleClearSearch = () => {
    setSearchValue('')
    if (onSearchChange) {
      onSearchChange('')
    } else {
      table.getColumn('name')?.setFilterValue('')
    }
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-4">
        <div className="flex items-center space-x-3">
          <Input
            placeholder="Tìm kiếm..."
            value={searchValue}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className="h-10 w-[150px] lg:w-[450px]"
          />
          {onSearchChange && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSearch}
              className="h-10 px-3"
            >
              <Search className="h-4 w-4" />
            </Button>
          )}
          {searchValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearSearch}
              className="h-8 px-2"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        {filterComponent}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        {onCreateItem && (
          <Button
            variant="default"
            size="sm"
            className="h-8"
            onClick={() => onCreateItem({})}
          >
            Create
          </Button>
        )}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
}