'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Loader2 } from 'lucide-react';
import { apiCreateMood, CreateMoodData } from '@/api/apiMood';
import { toast } from 'sonner';

interface CreateMoodDialogProps {
  onMoodCreated: () => void;
}

export function CreateMoodDialog({ onMoodCreated }: CreateMoodDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateMoodData>({
    name: '',
    description: '',
  });

  const handleInputChange = (field: keyof CreateMoodData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Vui lòng nhập tên mood');
      return;
    }

    if (!formData.description.trim()) {
      toast.error('Vui lòng nhập mô tả mood');
      return;
    }

    try {
      setLoading(true);
      await apiCreateMood(formData);
      toast.success('Tạo mood thành công');
      setOpen(false);
      setFormData({ name: '', description: '' });
      onMoodCreated();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi tạo mood');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      setOpen(newOpen);
      if (!newOpen) {
        setFormData({ name: '', description: '' });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Thêm mood
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Thêm mood mới</DialogTitle>
            <DialogDescription>
              Tạo mood mới cho hệ thống. Điền thông tin bên dưới.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Tên mood *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên mood..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Mô tả *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Nhập mô tả mood..."
                rows={3}
                disabled={loading}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Tạo mood
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
