'use client';

import React, { useState, useEffect } from 'react';
import { DataTable } from '@/components/data-table';
import { getAdminMoodColumns } from './columns';
import { EditMoodDialog } from './edit-mood-dialog';
import { DeleteMoodDialog } from './delete-mood-dialog';
import { CreateMoodDialog } from './create-mood-dialog';
import { apiGetMoods, Mood } from '@/api/apiMood';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Heart } from 'lucide-react';

export default function AdminMoodTable() {
  const [moods, setMoods] = useState<Mood[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editMood, setEditMood] = useState<Mood | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteMood, setDeleteMood] = useState<Mood | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  // Pagination and search states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalMoods, setTotalMoods] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [pageSize] = useState(10); // Fixed page size

  // Fetch moods data with pagination and search
  const fetchMoods = async (page: number = currentPage, search: string = searchQuery) => {
    try {
      setLoading(true);
      setError(null);
      const searchParam = search.trim();
      const response = await apiGetMoods({
        page,
        limit: pageSize,
        ...(searchParam && { search: searchParam }),
      });
      setMoods(response.items);
      setTotalMoods(response.total);
    } catch (err) {
      setError('Không thể tải danh sách mood');
      toast.error('Không thể tải danh sách mood');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle retry
  const handleRetry = () => {
    fetchMoods();
  };

  // Handle edit mood
  const handleEditMood = (mood: Mood) => {
    setEditMood(mood);
    setEditDialogOpen(true);
  };

  // Handle delete mood
  const handleDeleteMood = (mood: Mood) => {
    setDeleteMood(mood);
    setDeleteDialogOpen(true);
  };

  // Load data on component mount and when page/search changes
  useEffect(() => {
    fetchMoods(currentPage, searchQuery);
  }, [currentPage, searchQuery]);

  // Get columns with action handlers
  const columns = getAdminMoodColumns({
    editFunction: handleEditMood,
    deleteFunction: handleDeleteMood,
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Quản lý mood
          </CardTitle>
          <CardDescription>
            Quản lý các mood trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Đang tải dữ liệu...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Heart className="h-5 w-5" />
            Lỗi tải dữ liệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Thử lại
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 pb-20">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Quản lý mood
              </CardTitle>
            </div>
            <CreateMoodDialog onMoodCreated={() => fetchMoods()} />
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={moods}
            manualPagination={true}
            totalItems={totalMoods}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={handlePageChange}
            onSearchChange={handleSearch}
          />
        </CardContent>

        <EditMoodDialog
          mood={editMood}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={() => fetchMoods()}
        />

        <DeleteMoodDialog
          mood={deleteMood}
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          onSuccess={() => fetchMoods()}
        />
      </Card>
    </div>
  );
}
