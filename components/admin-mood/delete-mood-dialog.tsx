'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader2, AlertTriangle } from 'lucide-react';
import { apiDeleteMood, Mood } from '@/api/apiMood';
import { toast } from 'sonner';

interface DeleteMoodDialogProps {
  mood: Mood | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function DeleteMoodDialog({ mood, open, onOpenChange, onSuccess }: DeleteMoodDialogProps) {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!mood) return;

    try {
      setLoading(true);
      await apiDeleteMood(mood.slug);
      toast.success('Xóa mood thành công');
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi xóa mood');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      onOpenChange(newOpen);
    }
  };

  if (!mood) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Xác nhận xóa mood
          </DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn xóa mood này không? Hành động này không thể hoàn tác.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div>
              <span className="font-medium text-gray-700">Tên:</span>
              <span className="ml-2">{mood.name}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Slug:</span>
              <span className="ml-2 font-mono text-sm">{mood.slug}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Mô tả:</span>
              <span className="ml-2">{mood.description}</span>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <strong>Cảnh báo:</strong> Việc xóa mood này có thể ảnh hưởng đến các bài hát đang sử dụng mood này.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={loading}
          >
            Hủy
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xóa mood
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
