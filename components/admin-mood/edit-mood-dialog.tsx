'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { apiUpdateMood, Mood, UpdateMoodData } from '@/api/apiMood';
import { toast } from 'sonner';

interface EditMoodDialogProps {
  mood: Mood | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function EditMoodDialog({ mood, open, onOpenChange, onSuccess }: EditMoodDialogProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<UpdateMoodData>({
    name: '',
    description: '',
  });

  // Update form data when mood changes
  useEffect(() => {
    if (mood) {
      setFormData({
        name: mood.name,
        description: mood.description,
      });
    }
  }, [mood]);

  const handleInputChange = (field: keyof UpdateMoodData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!mood) return;

    if (!formData.name.trim()) {
      toast.error('Vui lòng nhập tên mood');
      return;
    }

    if (!formData.description.trim()) {
      toast.error('Vui lòng nhập mô tả mood');
      return;
    }

    try {
      setLoading(true);
      await apiUpdateMood(mood.slug, formData);
      toast.success('Cập nhật mood thành công');
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật mood');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      onOpenChange(newOpen);
    }
  };

  if (!mood) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Chỉnh sửa mood</DialogTitle>
            <DialogDescription>
              Cập nhật thông tin mood. Thay đổi thông tin bên dưới.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Tên mood *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên mood..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-description">Mô tả *</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Nhập mô tả mood..."
                rows={3}
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label>Slug</Label>
              <Input
                value={mood.slug}
                disabled
                className="bg-gray-50 text-gray-500"
              />
              <p className="text-xs text-gray-500">
                Slug không thể thay đổi sau khi tạo
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Cập nhật
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
