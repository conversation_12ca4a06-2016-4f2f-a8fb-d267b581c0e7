'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Mood } from '@/api/apiMood';

interface ColumnProps {
  editFunction: (mood: Mood) => void;
  deleteFunction: (mood: Mood) => void;
}

export const getAdminMoodColumns = ({ editFunction, deleteFunction }: ColumnProps): ColumnDef<Mood>[] => [
  {
    accessorKey: 'name',
    header: 'Tên',
    cell: ({ row }) => {
      const mood = row.original;
      return (
        <div className="font-medium">
          {mood.name}
        </div>
      );
    },
  },
  {
    accessorKey: 'description',
    header: '<PERSON><PERSON> tả',
    cell: ({ row }) => {
      const mood = row.original;
      return (
        <div className="max-w-[300px] truncate text-gray-600">
          {mood.description}
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: 'Thao tác',
    cell: ({ row }) => {
      const mood = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Mở menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => editFunction(mood)}>
              <Edit className="mr-2 h-4 w-4" />
              Chỉnh sửa
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => deleteFunction(mood)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Xóa
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
