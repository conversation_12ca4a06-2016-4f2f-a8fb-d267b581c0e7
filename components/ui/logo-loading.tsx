import React from 'react';

interface LogoLoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LogoLoading({ 
  message = "Đang tải...", 
  size = 'md',
  className = "" 
}: LogoLoadingProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-16 w-16', 
    lg: 'h-24 w-24'
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className="relative">
        {/* Logo với animation float mượt mà */}
        <div className="animate-bounce duration-1000">
          <img
            src="/logo/vncm.png"
            alt="Loading..."
            className={`${sizeClasses[size]} object-contain transition-all duration-300 hover:scale-110`}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/logo/vncmblack.png";
            }}
          />
        </div>

        {/* Thêm hiệu ứng ring xung quanh logo */}
        <div className={`absolute inset-0 ${sizeClasses[size]} rounded-full border-2 border-orange-300 animate-ping opacity-30`}></div>
        <div className={`absolute inset-0 ${sizeClasses[size]} rounded-full border border-orange-200 animate-pulse opacity-40`}></div>
      </div>

      {message && (
        <div className="mt-6 text-center">
          <span className="text-gray-600 text-sm animate-pulse">
            {message}
          </span>
          <div className="flex justify-center mt-2 space-x-1">
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>
        </div>
      )}
    </div>
  );
}
