'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { LogIn, UserPlus } from 'lucide-react';

interface LoginDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  message?: string;
}

export const LoginDialog: React.FC<LoginDialogProps> = ({
  open,
  onOpenChange,
  message = "Bạn cần đăng nhập để tải xuống bài hát"
}) => {
  const router = useRouter();

  const handleLogin = () => {
    onOpenChange(false);
    router.push('/login');
  };

  const handleRegister = () => {
    onOpenChange(false);
    router.push('/register');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-semibold text-gray-900">
            Yêu cầu đăng nhập
          </DialogTitle>
          <DialogDescription className="text-center text-gray-600 mt-2">
            {message}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col gap-3 mt-6">
          <Button 
            onClick={handleLogin}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
          >
            <LogIn className="w-4 h-4 mr-2" />
            Đăng nhập
          </Button>
          
          <Button 
            onClick={handleRegister}
            variant="outline"
            className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            <UserPlus className="w-4 h-4 mr-2" />
            Đăng ký tài khoản
          </Button>
          
          <Button 
            onClick={() => onOpenChange(false)}
            variant="ghost"
            className="w-full text-gray-500 hover:text-gray-700"
          >
            Để sau
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
