"use client";

import { useState, KeyboardEvent, useRef, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";

interface TagsInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  maxTags?: number;
}

export const TagsInput = ({
  value,
  onChange,
  placeholder = "Add tags...",
  disabled = false,
  maxTags = 10
}: TagsInputProps) => {
  const [inputValue, setInputValue] = useState("");
  const [inputKey, setInputKey] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  const addTagFromInput = (inputText: string) => {
    const trimmedValue = inputText.trim();

    if (trimmedValue && !value.includes(trimmedValue) && value.length < maxTags) {
      const newTags = [...value, trimmedValue];
      onChange(newTags);
      setInputValue("");
      setInputKey(prev => prev + 1);
      return true;
    }
    return false;
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      addTagFromInput(inputValue);
    } else if (e.key === "," || e.key === ";") {
      e.preventDefault();
      e.stopPropagation();
      addTagFromInput(inputValue);
    } else if (e.key === "Backspace" && inputValue === "" && value.length > 0) {
      e.preventDefault();
      const newTags = [...value];
      newTags.pop();
      onChange(newTags);
    }
  };

  const addTag = () => {
    return addTagFromInput(inputValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    if (newValue.includes(',') || newValue.includes(';')) {
      const parts = newValue.split(/[,;]/);
      const validParts: string[] = [];

      parts.forEach((part) => {
        const trimmedPart = part.trim();
        if (trimmedPart && !value.includes(trimmedPart) && (value.length + validParts.length) < maxTags) {
          validParts.push(trimmedPart);
        }
      });

      if (validParts.length > 0) {
        onChange([...value, ...validParts]);
      }
      setInputValue('');
    } else {
      setInputValue(newValue);
    }
  };

  const removeTag = (tagToRemove: string) => {
    onChange(value.filter(tag => tag !== tagToRemove));
  };

  const handleContainerClick = () => {
    if (inputRef.current && !disabled) {
      inputRef.current.focus();
    }
  };

  return (
    <div
      className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[40px] focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 bg-background cursor-text"
      onClick={handleContainerClick}
    >
      {value.map((tag, index) => (
        <Badge key={`${tag}-${index}`} variant="secondary" className="flex items-center gap-1 px-2 py-1">
          <span className="text-sm">{tag}</span>
          {!disabled && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                removeTag(tag);
              }}
              className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-muted transition-colors"
            >
              <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
            </button>
          )}
        </Badge>
      ))}
      <Input
        key={inputKey}
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onBlur={() => {
          addTag();
        }}
        placeholder={
          value.length === 0
            ? placeholder
            : value.length >= maxTags
              ? `Max ${maxTags} tags`
              : "Add tag..."
        }
        disabled={disabled || value.length >= maxTags}
        className="flex-1 border-none shadow-none focus-visible:ring-0 min-w-[120px] bg-transparent"
      />
      {value.length > 0 && (
        <div className="text-xs text-muted-foreground self-center">
          {value.length}/{maxTags}
        </div>
      )}
    </div>
  );
};
