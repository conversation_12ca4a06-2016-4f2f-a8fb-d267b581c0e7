'use client';

import React, { createContext, useContext, useState } from 'react';
import { LoginDialog } from '@/components/ui/login-dialog';

interface LoginDialogContextType {
  showLoginDialog: (message?: string) => void;
}

const LoginDialogContext = createContext<LoginDialogContextType | undefined>(undefined);

export const useLoginDialog = () => {
  const context = useContext(LoginDialogContext);
  if (!context) {
    throw new Error('useLoginDialog must be used within LoginDialogProvider');
  }
  return context;
};

interface LoginDialogProviderProps {
  children: React.ReactNode;
}

export const LoginDialogProvider: React.FC<LoginDialogProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('Bạn cần đăng nhập để tải xuống bài hát');

  const showLoginDialog = (customMessage?: string) => {
    if (customMessage) {
      setMessage(customMessage);
    }
    setIsOpen(true);
  };

  return (
    <LoginDialogContext.Provider value={{ showLoginDialog }}>
      {children}
      <LoginDialog 
        open={isOpen}
        onOpenChange={setIsOpen}
        message={message}
      />
    </LoginDialogContext.Provider>
  );
};
