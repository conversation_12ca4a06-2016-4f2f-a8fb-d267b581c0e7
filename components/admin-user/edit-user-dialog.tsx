'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { apiUpdateUser } from '@/api/apiUser';
import { User } from '@/api/apiAuth';
import { Loader2, X } from 'lucide-react';

const editUserSchema = z.object({
  name: z.string().min(1, 'Tên không được để trống').max(100, 'Tên không được quá 100 ký tự'),
  email: z.string().email('Email không hợp lệ'),
  tier: z.enum(['FREE', 'PRO', 'BUSINESS'], {
    required_error: 'Vui lòng chọn gói dịch vụ',
  }),
  roleLst: z.array(z.string()).min(1, 'Phải có ít nhất một vai trò'),
});

type EditUserFormData = z.infer<typeof editUserSchema>;

interface EditUserDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function EditUserDialog({ user, open, onOpenChange, onSuccess }: EditUserDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);

  const form = useForm<EditUserFormData>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      name: '',
      email: '',
      tier: 'FREE',
      roleLst: [],
    },
  });

  // Reset form when user changes
  useEffect(() => {
    if (user) {
      form.reset({
        name: user.name,
        email: user.email,
        tier: user.tier,
        roleLst: user.roleLst,
      });
      setSelectedRoles(user.roleLst);
    }
  }, [user, form]);

  const handleAddRole = (role: string) => {
    if (!selectedRoles.includes(role)) {
      const newRoles = [...selectedRoles, role];
      setSelectedRoles(newRoles);
      form.setValue('roleLst', newRoles);
    }
  };

  const handleRemoveRole = (roleToRemove: string) => {
    const newRoles = selectedRoles.filter(role => role !== roleToRemove);
    setSelectedRoles(newRoles);
    form.setValue('roleLst', newRoles);
  };

  const onSubmit = async (data: EditUserFormData) => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Only send changed fields (excluding password and other sensitive fields)
      const updateData: Partial<EditUserFormData> = {};

      if (data.name !== user.name) updateData.name = data.name;
      if (data.email !== user.email) updateData.email = data.email;
      if (data.tier !== user.tier) updateData.tier = data.tier;
      if (JSON.stringify(data.roleLst.sort()) !== JSON.stringify(user.roleLst.sort())) {
        updateData.roleLst = data.roleLst;
      }

      // If no changes, just close dialog
      if (Object.keys(updateData).length === 0) {
        toast.info('Không có thay đổi nào để cập nhật');
        onOpenChange(false);
        return;
      }

      await apiUpdateUser(user.slug, updateData);

      toast.success('Cập nhật thông tin người dùng thành công');
      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      // Display detailed error message
      let errorMessage = 'Không thể cập nhật thông tin người dùng';
      if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const availableRoles = ['user', 'admin'];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa thông tin người dùng</DialogTitle>
          <DialogDescription>
            Cập nhật thông tin cho người dùng: <strong>{user?.name}</strong>
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên người dùng</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập tên người dùng" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập email" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="tier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gói dịch vụ</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn gói" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="FREE">Miễn phí</SelectItem>
                        <SelectItem value="PRO">Pro</SelectItem>
                        <SelectItem value="BUSINESS">Business</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="roleLst"
              render={() => (
                <FormItem>
                  <FormLabel>Vai trò</FormLabel>
                  <div className="space-y-2">
                    <Select onValueChange={handleAddRole}>
                      <SelectTrigger>
                        <SelectValue placeholder="Thêm vai trò" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableRoles
                          .filter(role => !selectedRoles.includes(role))
                          .map(role => (
                            <SelectItem key={role} value={role}>
                              {role === 'admin' ? 'Quản trị viên' : 'Người dùng'}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    
                    <div className="flex flex-wrap gap-2">
                      {selectedRoles.map(role => (
                        <Badge key={role} variant="secondary" className="flex items-center gap-1">
                          {role === 'admin' ? 'Quản trị viên' : 'Người dùng'}
                          <X 
                            className="h-3 w-3 cursor-pointer hover:text-red-500" 
                            onClick={() => handleRemoveRole(role)}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Hủy
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Cập nhật
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
