import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { User } from "@/api/apiAuth";

type Props = {
  editFunction?: (user: User) => void;
};

export const getAdminUserColumns = (props: Props): ColumnDef<User>[] => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tên người dùng" />
      ),
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
            <span className="text-sm font-medium text-blue-600">
              {(row.getValue("name") as string)?.charAt(0)?.toUpperCase()}
            </span>
          </div>
          <div className="font-medium text-gray-900">{row.getValue("name")}</div>
        </div>
      ),
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => (
        <div className="text-gray-600">{row.getValue("email")}</div>
      ),
    },
    {
      accessorKey: "roleLst",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Vai trò" />
      ),
      cell: ({ row }) => {
        const roles = row.getValue("roleLst") as string[];
        return (
          <div className="flex flex-wrap gap-1">
            {roles.map((role, index) => (
              <span
                key={index}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  role === 'admin'
                    ? 'bg-purple-100 text-purple-800 border border-purple-200'
                    : 'bg-blue-100 text-blue-800 border border-blue-200'
                }`}
              >
                {role === 'admin' ? 'Quản trị viên' : 'Người dùng'}
              </span>
            ))}
          </div>
        );
      },
      filterFn: (row, id, value) => {
        const roles = row.getValue(id) as string[];
        return value.some((v: string) => roles.includes(v));
      },
    },
    {
      accessorKey: "tier",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Gói dịch vụ" />
      ),
      cell: ({ row }) => {
        const tier = row.getValue("tier") as string;
        const getTierConfig = (tier: string) => {
          switch (tier) {
            case "FREE":
              return {
                label: "Miễn phí",
                className: "bg-gray-100 text-gray-800 border-gray-200"
              }
            case "PRO":
              return {
                label: "Pro",
                className: "bg-blue-100 text-blue-800 border-blue-200"
              }
            case "BUSINESS":
              return {
                label: "Business",
                className: "bg-orange-100 text-orange-800 border-orange-200"
              }
            default:
              return {
                label: tier,
                className: "bg-gray-100 text-gray-800 border-gray-200"
              }
          }
        }

        const config = getTierConfig(tier)
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
            {config.label}
          </span>
        )
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const item = row.original;

        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => props.editFunction?.(item)}
            className="h-8 w-8 p-0 hover:bg-orange-50 hover:text-orange-600 rounded-full"
          >
            <span className="sr-only">Chỉnh sửa người dùng</span>
            <Edit className="h-4 w-4" />
          </Button>
        );
      },
    },
  ];
};
