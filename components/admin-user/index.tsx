'use client';

import React, { useState, useEffect } from 'react';
import { DataTable } from '@/components/data-table';
import { getAdminUserColumns } from './columns';
import { EditUserDialog } from './edit-user-dialog';
import { apiGetUsers } from '@/api/apiUser';
import { User } from '@/api/apiAuth';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Users } from 'lucide-react';

export default function AdminUserTable() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editUser, setEditUser] = useState<User | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  // Pagination and search states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [pageSize] = useState(10); // Fixed page size

  // Fetch users data with pagination and search
  const fetchUsers = async (page: number = currentPage, search: string = searchQuery) => {
    try {
      setLoading(true);
      setError(null);

      // Chỉ gửi search parameter khi có giá trị
      const searchParam = search.trim();
      const response = await apiGetUsers({
        page,
        limit: pageSize,
        ...(searchParam && { search: searchParam }),
      });
      setUsers(response.items);
      setTotalUsers(response.total);
    } catch (err) {
      setError('Không thể tải danh sách người dùng');
      toast.error('Không thể tải danh sách người dùng');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle retry
  const handleRetry = () => {
    fetchUsers();
  };

  // Handle edit user
  const handleEditUser = (user: User) => {
    setEditUser(user);
    setEditDialogOpen(true);
  };

  // Load data on component mount and when page/search changes
  useEffect(() => {
    fetchUsers(currentPage, searchQuery);
  }, [currentPage, searchQuery]);

  // Get columns with action handlers
  const columns = getAdminUserColumns({
    editFunction: handleEditUser,
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Quản lý người dùng
          </CardTitle>
          <CardDescription>
            Quản lý thông tin và trạng thái của tất cả người dùng trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Đang tải dữ liệu...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Users className="h-5 w-5" />
            Lỗi tải dữ liệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Thử lại
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 pb-20">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Quản lý người dùng
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={users}
            manualPagination={true}
            totalItems={totalUsers}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={handlePageChange}
            onSearchChange={handleSearch}
          />
        </CardContent>

        <EditUserDialog
          user={editUser}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={() => fetchUsers()}
        />
      </Card>

    </div>
  );
}
