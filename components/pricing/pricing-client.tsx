"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Star, ArrowRight, CircleCheckBig } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

interface PricingPlan {
  id: string
  name: string
  description: string
  color: string
  backgroundColor: string
  price: {
    monthly: number | string
    yearly: number | string
  }
  originalPrice?: {
    yearly: number
  }
  duration: string
  popular: boolean
  features: string[]
  buttonText: string
  tier: number
}

export const plans: PricingPlan[] = [
  {
    id: "free",
    name: "MIỄN PHÍ",
    description: "<PERSON>àn hảo cho người sáng tạo c<PERSON> nhân",
    color: "#FFFFFF",
    backgroundColor: "#9C9C9C",
    price: {
      monthly: "MIỄN PHÍ",
      yearly: "MIỄN PHÍ",
    },
    duration: "Vĩnh viễn",
    popular: false,
    features: [
      "Truy cập kho nhạc Miễn phí",
      "Sử dụng mục đích cá nhân",
      "Giới hạn 5 lượt tải / ngày",
      "Giới hạn trên 1 kênh",
    ],
    buttonText: "Đã đăng ký",
    tier: 0,
  },
  {
    id: "pro",
    name: "PRO",
    description: "Dành cho người sáng tạo chuyên nghiệp",
    color: "#B1FF0A",
    backgroundColor: "#B1FF0A",
    price: {
      monthly: 299000,
      yearly: 2990000,
    },
    originalPrice: {
      yearly: 3588000,
    },
    duration: "1 năm",
    popular: true,
    features: [
      "Truy cập kho nhạc Miễn phí",
      "Truy cập kho nhạc Pro",
      "Sử dụng mục đích cá nhân",
      "Không giới hạn lượt tải",
      "Bật kiếm tiền trên các MXH",
      "Hỗ trợ Whitelist trên 1 kênh",
    ],
    buttonText: "Đăng ký",
    tier: 1,
  },
  {
    id: "business",
    name: "BUSINESS",
    description: "Giải pháp tùy chỉnh cho doanh nghiệp",
    color: "#A54DF1",
    backgroundColor: "#A54DF1",
    price: {
      monthly: 599000,
      yearly: 5990000,
    },
    originalPrice: {
      yearly: 7188000,
    },
    duration: "Vĩnh viễn",
    popular: false,
    features: [
      "Truy cập kho nhạc Miễn phí",
      "Truy cập kho nhạc Pro",
      "Truy cập kho nhạc Business",
      "Sử dụng thương mại",
      "Chạy quảng cáo",
      "Không giới hạn lượt tải",
      "Bật kiếm tiền trên các MXH",
      "Hỗ trợ Whitelist trên 5 kênh",
    ],
    buttonText: "Đăng ký",
    tier: 2,
  },
]

export function PricingClient() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("yearly")
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null)

  const formatPrice = (price: number | string) => {
    if (typeof price === "string") return price
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price)
  }

  const faqItems = [
    {
      question: "Tôi có thể bật kiếm tiền khi sử dụng kho nhạc miễn phí không?",
      answer:
        "Không, gói miễn phí không hỗ trợ kiếm tiền. Bạn cần nâng cấp lên gói PRO hoặc BUSINESS để bật tính năng này.",
    },
    {
      question: "Tôi có thể bật kiếm tiền trên nhiều nền tảng YouTube, Facebook, TikTok.. không?",
      answer:
        "Có, gói PRO hỗ trợ kiếm tiền trên 1 kênh mỗi nền tảng, trong khi gói BUSINESS hỗ trợ trên 5 kênh mỗi nền tảng.",
    },
    {
      question: "Whitelist là gì? Tại sao lại cần Whitelist?",
      answer:
        "Whitelist là quá trình thêm kênh của bạn vào danh sách cho phép để tránh các khiếu nại bản quyền khi sử dụng nhạc của chúng tôi. Điều này đảm bảo bạn có thể kiếm tiền từ nội dung của mình mà không gặp rắc rối.",
    },
    {
      question: "Tôi muốn sử dụng nhạc trong video quảng cáo của mình?",
      answer:
        "Để sử dụng nhạc trong video quảng cáo, bạn cần đăng ký gói BUSINESS. Gói này cung cấp quyền sử dụng thương mại và chạy quảng cáo.",
    },
    {
      question: "Có cần phải ghi nguồn cho các bản nhạc đã sử dụng không?",
      answer:
        "Tùy thuộc vào gói bạn chọn. Với các gói trả phí, bạn không cần phải ghi nguồn. Tuy nhiên, chúng tôi luôn khuyến khích việc ghi nguồn để ủng hộ người sáng tạo.",
    },
  ]

  return (
    <div className="max-w-7xl mx-auto">
      <div className="px-4 sm:px-6 lg:px-8 py-8 md:py-16">
        <div className="text-center mb-8 md:mb-16">
          <h1 className="text-3xl md:text-4xl lg:text-5xl text-[#1E1E1E] mb-4 md:mb-6">
            Chọn gói phù hợp với nhu cầu của bạn
          </h1>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-6 md:mb-8">
            <div className="flex items-center gap-4">
              <span className={`text-sm font-semibold ${billingCycle === "monthly" ? "text-black" : "text-gray-500"}`}>
                Theo tháng
              </span>
              <button
                onClick={() => setBillingCycle(billingCycle === "monthly" ? "yearly" : "monthly")}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ${billingCycle === "yearly" ? "bg-[#A54DF1]" : "bg-[#A54DF1]"
                  }`}
                aria-label="Toggle billing cycle"
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 ${billingCycle === "yearly" ? "translate-x-6" : "translate-x-1"
                    }`}
                />
              </button>
              <span className={`text-sm font-semibold ${billingCycle === "yearly" ? "text-black" : "text-gray-500"}`}>
                Theo năm
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative border rounded-xl transition-all duration-300  border-black`}
            >
              <CardHeader className="text-center pb-4 pt-8">
                <div className="flex justify-center mb-4">
                  <div
                    className={`p-3 rounded-full bg-black`}
                  >
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.0001 1.66666L12.5751 6.88333L18.3334 7.725L14.1667 11.7833L15.1501 17.5167L10.0001 14.8083L4.85008 17.5167L5.83341 11.7833L1.66675 7.725L7.42508 6.88333L10.0001 1.66666Z" stroke={plan.color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>

                  </div>

                </div>
                <CardTitle
                  className={`text-3xl font-extrabold uppercase mb-2 
                    `}
                >
                  <div className="relative inline-block px-4 mb-10">
                    {/* <div className={`absolute inset-0 top-3 bg-[#B1FF0A] rounded-md h-8 w-full`} /> */}
                    <div className={`absolute inset-0 top-3 bg-[${plan.backgroundColor}] rounded-md h-10 w-full`} />
                    <span className="relative z-10 text-black font-extrabold text-3xl uppercase">
                      {plan.name}
                    </span>
                  </div>
                </CardTitle>
                <CardDescription className="text-gray-600 ">{plan.description}</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-center mb-6">
                  <div className="flex items-baseline justify-center gap-2">
                    <span className="text-4xl font-bold text-black">{formatPrice(plan.price[billingCycle])}</span>
                    {typeof plan.price[billingCycle] === "number" && (
                      <span className="text-gray-500">/{billingCycle === "monthly" ? "tháng" : "năm"}</span>
                    )}
                  </div>
                  {billingCycle === "yearly" && plan.originalPrice?.yearly && (
                    <div className="text-sm text-gray-500 line-through mt-1">
                      {formatPrice(plan.originalPrice.yearly)}
                    </div>
                  )}
                  <div className="text-sm text-gray-600 mt-2">Thời hạn: {plan.duration}</div>
                </div>
                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Check className="h-5 w-5 text-v0-purple-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                {plan.id === "free" ? (
                  <Button className="w-full py-3 bg-gray-300 text-gray-700 cursor-not-allowed" disabled>
                    {plan.buttonText}
                    <CircleCheckBig className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    className="w-full py-3 bg-black text-white hover:bg-gray-800"
                  >
                    {plan.buttonText}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        <p className="text-center text-gray-600 mt-12 text-lg">
          Nhu cầu Phim Chiếu Rạp, TV Show, Truyền Hình, Game, Apps vui lòng{" "}
          <span className="font-semibold text-v0-purple-600">liên hệ</span>
        </p>

        <div className="mt-16 max-w-4xl mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-black text-center mb-8">CÂU HỎI THƯỜNG GẶP</h2>
          <div className="flex flex-col items-center">
            <Accordion type="single" collapsible className="w-full max-w-4xl">
              {faqItems.map((item, index) => (
                <AccordionItem key={index} value={`item-${index}`} className="border-none">
                  <AccordionTrigger className="text-lg font-semibold text-gray-800 hover:no-underline flex justify-center text-center pb-4 mb-4 [&>svg]:hidden border-none">
                    <span
                      className="inline-block"
                    >
                      {item.question}
                    </span>
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600 text-base text-center pb-6">{item.answer}</AccordionContent>
                  <div className="flex justify-center mb-6">
                    <div
                      className="h-px bg-black"
                      style={{
                        width: 'fit-content',
                        minWidth: '0'
                      }}
                    >
                      <span className="invisible text-lg font-semibold">{item.question}</span>
                    </div>
                  </div>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>

        <Dialog open={!!selectedPlan} onOpenChange={() => setSelectedPlan(null)}>
          <DialogContent className="bg-white text-black max-w-sm mx-auto rounded-xl shadow-lg">
            <DialogHeader>
              <DialogTitle className="text-v0-purple-600 text-2xl font-bold text-center">
                Liên hệ tư vấn ngay!
              </DialogTitle>
              <DialogDescription className="text-center text-gray-600 mt-1">
                Gói bạn chọn: <strong>{selectedPlan?.name}</strong>. Chúng tôi sẵn sàng hỗ trợ qua Zalo.
              </DialogDescription>
            </DialogHeader>
            <div className="flex flex-col items-center gap-4 mt-4">
              <img
                src="/placeholder.svg?height=80&width=80"
                alt="Zalo"
                className="w-20 h-20 rounded-full border border-gray-200"
              />
              <a
                href="https://zalo.me/0911830222"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block bg-v0-purple-600 text-white font-semibold px-6 py-2 rounded-lg hover:bg-v0-purple-700 transition"
              >
                Mở Zalo: 0911830222
              </a>
            </div>
            <div className="mt-6 text-center">
              <Button
                variant="outline"
                className="border-gray-300 text-gray-700 hover:bg-gray-100 bg-transparent"
                onClick={() => setSelectedPlan(null)}
              >
                Đóng
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
