'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Genre } from '@/api/apiGenre';

interface ColumnProps {
  editFunction: (genre: Genre) => void;
  deleteFunction: (genre: Genre) => void;
}

export const getAdminGenreColumns = ({ editFunction, deleteFunction }: ColumnProps): ColumnDef<Genre>[] => [
  {
    accessorKey: 'name',
    header: 'Tên',
    cell: ({ row }) => {
      const genre = row.original;
      return (
        <div className="font-medium">
          {genre.name}
        </div>
      );
    },
  },
  {
    accessorKey: 'description',
    header: '<PERSON><PERSON> tả',
    cell: ({ row }) => {
      const genre = row.original;
      return (
        <div className="max-w-[300px] truncate text-gray-600">
          {genre.description}
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: 'Thao tác',
    cell: ({ row }) => {
      const genre = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Mở menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => editFunction(genre)}>
              <Edit className="mr-2 h-4 w-4" />
              Chỉnh sửa
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => deleteFunction(genre)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Xóa
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
