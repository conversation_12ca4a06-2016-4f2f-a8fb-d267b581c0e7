'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { apiUpdateGenre, Genre, UpdateGenreData } from '@/api/apiGenre';
import { toast } from 'sonner';

interface EditGenreDialogProps {
  genre: Genre | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function EditGenreDialog({ genre, open, onOpenChange, onSuccess }: EditGenreDialogProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<UpdateGenreData>({
    name: '',
    description: '',
  });

  // Update form data when genre changes
  useEffect(() => {
    if (genre) {
      setFormData({
        name: genre.name,
        description: genre.description,
      });
    }
  }, [genre]);

  const handleInputChange = (field: keyof UpdateGenreData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!genre) return;

    if (!formData.name.trim()) {
      toast.error('Vui lòng nhập tên thể loại');
      return;
    }

    if (!formData.description.trim()) {
      toast.error('Vui lòng nhập mô tả thể loại');
      return;
    }

    try {
      setLoading(true);
      await apiUpdateGenre(genre.slug, formData);
      toast.success('Cập nhật thể loại thành công');
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật thể loại');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      onOpenChange(newOpen);
    }
  };

  if (!genre) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Chỉnh sửa thể loại</DialogTitle>
            <DialogDescription>
              Cập nhật thông tin thể loại. Thay đổi thông tin bên dưới.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Tên thể loại *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên thể loại..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-description">Mô tả *</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Nhập mô tả thể loại..."
                rows={3}
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label>Slug</Label>
              <Input
                value={genre.slug}
                disabled
                className="bg-gray-50 text-gray-500"
              />
              <p className="text-xs text-gray-500">
                Slug không thể thay đổi sau khi tạo
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Cập nhật
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
