'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Loader2 } from 'lucide-react';
import { apiCreateGenre, CreateGenreData } from '@/api/apiGenre';
import { toast } from 'sonner';

interface CreateGenreDialogProps {
  onGenreCreated: () => void;
}

export function CreateGenreDialog({ onGenreCreated }: CreateGenreDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateGenreData>({
    name: '',
    description: '',
  });

  const handleInputChange = (field: keyof CreateGenreData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Vui lòng nhập tên thể loại');
      return;
    }

    if (!formData.description.trim()) {
      toast.error('Vui lòng nhập mô tả thể loại');
      return;
    }

    try {
      setLoading(true);
      await apiCreateGenre(formData);
      toast.success('Tạo thể loại thành công');
      setOpen(false);
      setFormData({ name: '', description: '' });
      onGenreCreated();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi tạo thể loại');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      setOpen(newOpen);
      if (!newOpen) {
        setFormData({ name: '', description: '' });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Thêm thể loại
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Thêm thể loại mới</DialogTitle>
            <DialogDescription>
              Tạo thể loại mới cho hệ thống. Điền thông tin bên dưới.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Tên thể loại *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên thể loại..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Mô tả *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Nhập mô tả thể loại..."
                rows={3}
                disabled={loading}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Tạo thể loại
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
