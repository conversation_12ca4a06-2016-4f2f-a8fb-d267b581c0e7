'use client';

import React, { useState, useEffect } from 'react';
import { DataTable } from '@/components/data-table';
import { getAdminGenreColumns } from './columns';
import { EditGenreDialog } from './edit-genre-dialog';
import { DeleteGenreDialog } from './delete-genre-dialog';
import { CreateGenreDialog } from './create-genre-dialog';
import { apiGetGenres, Genre } from '@/api/apiGenre';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Disc3 } from 'lucide-react';

export default function AdminGenreTable() {
  const [genres, setGenres] = useState<Genre[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editGenre, setEditGenre] = useState<Genre | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteGenre, setDeleteGenre] = useState<Genre | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  // Pagination and search states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalGenres, setTotalGenres] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [pageSize] = useState(10); // Fixed page size

  // Fetch genres data with pagination and search
  const fetchGenres = async (page: number = currentPage, search: string = searchQuery) => {
    try {
      setLoading(true);
      setError(null);
      const searchParam = search.trim();
      const response = await apiGetGenres({
        page,
        limit: pageSize,
        ...(searchParam && { search: searchParam }),
      });
      setGenres(response.items);
      setTotalGenres(response.total);
    } catch (err) {
      setError('Không thể tải danh sách thể loại');
      toast.error('Không thể tải danh sách thể loại');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle retry
  const handleRetry = () => {
    fetchGenres();
  };

  // Handle edit genre
  const handleEditGenre = (genre: Genre) => {
    setEditGenre(genre);
    setEditDialogOpen(true);
  };

  // Handle delete genre
  const handleDeleteGenre = (genre: Genre) => {
    setDeleteGenre(genre);
    setDeleteDialogOpen(true);
  };

  // Load data on component mount and when page/search changes
  useEffect(() => {
    fetchGenres(currentPage, searchQuery);
  }, [currentPage, searchQuery]);

  // Get columns with action handlers
  const columns = getAdminGenreColumns({
    editFunction: handleEditGenre,
    deleteFunction: handleDeleteGenre,
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Disc3 className="h-5 w-5" />
            Quản lý thể loại
          </CardTitle>
          <CardDescription>
            Quản lý các thể loại nhạc trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Đang tải dữ liệu...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Disc3 className="h-5 w-5" />
            Lỗi tải dữ liệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Thử lại
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 pb-20">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Quản lý thể loại
              </CardTitle>
            </div>
            <CreateGenreDialog onGenreCreated={() => fetchGenres()} />
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={genres}
            manualPagination={true}
            totalItems={totalGenres}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={handlePageChange}
            onSearchChange={handleSearch}
          />
        </CardContent>

        <EditGenreDialog
          genre={editGenre}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={() => fetchGenres()}
        />

        <DeleteGenreDialog
          genre={deleteGenre}
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          onSuccess={() => fetchGenres()}
        />
      </Card>
    </div>
  );
}
