"use client"
import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useIsAuthenticated, useIsAdmin } from "@/store/authStore"

interface SidebarProps {
  isMobile?: boolean
  onClose?: () => void
}

const menuItems = [
  {
    title: "TRANG CHỦ",
    href: "/",
  },
  {
    title: "GÓI ĐĂNG KÝ",
    href: "/bang-gia",
  },
  {
    title: "NGHỆ SĨ",
    href: "/artists",
  },
  {
    title: "THỂ LOẠI",
    href: "/genres",
  },
  {
    title: "TÂM TRẠNG",
    href: "/moods",
  },
]

const adminItems = [
  {
    title: "DASHBOARD",
    href: "/admin/dashboard",
  },
  {
    title: "NGƯỜI DÙNG",
    href: "/admin/users",
  },
  {
    title: "BÀI HÁT",
    href: "/admin/songs",
  },
  {
    title: "NGHỆ SĨ",
    href: "/admin/artists",
  },
  {
    title: "THỂ LOẠI",
    href: "/admin/genres",
  },
  {
    title: "TÂM TRẠNG",
    href: "/admin/moods",
  },
]

export function Sidebar({ isMobile = false, onClose }: SidebarProps) {
  const pathname = usePathname()
  const isAuthenticated = useIsAuthenticated()
  const isAdmin = useIsAdmin()

  const handleItemClick = () => {
    if (isMobile && onClose) {
      onClose()
    }
  }

  return (
    <div className={cn(
      "flex flex-col h-full bg-white border-r border-gray-200",
      isMobile ? "w-full" : "w-64"
    )}>
      <div className="flex items-center justify-center px-6">
        <Link href="/" onClick={handleItemClick}>
           <img
          src="/logo/vncmblack.png"
          alt="VNCM Logo"
          className="h-32 w-auto object-contain"
        />
        </Link>
      </div>

      <nav className="flex-1 px-6 mt-20">
        <ul className="space-y-2">
          {(isAdmin ? adminItems : menuItems).map((item) => {
            const isActive = pathname === item.href

            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  onClick={handleItemClick}
                  className={cn(
                    "block text-md text-center font-medium transition-colors duration-200 p-4 rounded-full",
                    isActive
                      ? "text-black border-black border"
                      : "text-black hover:text-black hover:border hover:border-gray-950"
                  )}
                >
                  {item.title}
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>
    </div>
  )
}
