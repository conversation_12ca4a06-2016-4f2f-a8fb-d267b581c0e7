'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useMusicPlayer } from '@/contexts/MusicPlayerContext';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,

  Download,

  Music
} from 'lucide-react';
import { toast } from 'sonner';
import { useUser, useIsAuthenticated } from '@/store/authStore';
import { useLoginDialogStore } from '@/store/loginDialogStore';
import { Song } from '@/api/apiSong';
import { downloadFile } from '@/lib/utils';

// Download Format Dialog Component
const DownloadFormatDialog = ({ song, open, onOpenChange }: { song: Song; open: boolean; onOpenChange: (open: boolean) => void }) => {

  const handleDownloadFile = async (url: string, filename: string) => {
    try {
      // Hiển thị loading state
      toast.loading('Đang tải xuống...', { id: 'download' });
      await downloadFile(url);
      onOpenChange(false);
      toast.success('Tải xuống thành công!', { id: 'download' });
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Tải xuống thất bại. Vui lòng thử lại.', { id: 'download' });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg p-0 overflow-hidden bg-white rounded-2xl shadow-2xl border-0">
        <div className="relative px-8 pt-8 pb-6">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center shadow-lg">
              <Download className="w-8 h-8 text-black" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Chọn định dạng</h2>
            <p className="text-gray-500 text-sm">Tải xuống bài hát với định dạng phù hợp</p>
          </div>
        </div>
        <div className="mx-8 mb-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
          <div className="flex items-center gap-4">
            <div className="w-14 h-14 rounded-xl overflow-hidden bg-gradient-to-br from-orange-400 to-pink-500 flex items-center justify-center flex-shrink-0 shadow-md">
              {song.imageUrl ? (
                <img
                  src={song.imageUrl}
                  alt={song.songName}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              ) : (
                <Music className="w-7 h-7 text-white" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate text-base">{song.songName}</h3>
              <p className="text-gray-600 truncate text-sm">{song.artistName}</p>
            </div>
          </div>
        </div>

        {/* Format Options */}
        <div className="px-8 pb-8 space-y-4">
          {/* MP3 Option */}
          {song.mp3Url ? (
            <button
              onClick={() => handleDownloadFile(song.mp3Url!, song.songName)}
              className="group relative p-5 rounded-2xl border-2 transition-all duration-300 border-orange-200 bg-white hover:border-orange-300 hover:shadow-lg hover:shadow-orange-100 hover:-translate-y-1 block w-full text-left"
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                  <span className="text-white font-bold text-sm">MP3</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-gray-900">MP3 Audio</h4>
                    <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs font-medium rounded-full">
                      Phổ biến
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Kích thước nhỏ, tương thích cao với mọi thiết bị
                  </p>
                </div>
                <div className="text-orange-500">
                  <Download className="w-5 h-5" />
                </div>
              </div>
            </button>
          ) : (
            <div className="group relative p-5 rounded-2xl border-2 transition-all duration-300 border-gray-200 bg-gray-50 cursor-not-allowed opacity-60">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                  <span className="text-white font-bold text-sm">MP3</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-gray-900">MP3 Audio</h4>
                    <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs font-medium rounded-full">
                      Phổ biến
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Không có sẵn
                  </p>
                </div>
                <div className="text-gray-400">
                  <Download className="w-5 h-5" />
                </div>
              </div>
            </div>
          )}

          {/* WAV Option */}
          {song.fileUrl ? (
            <button
              onClick={() => handleDownloadFile(song.fileUrl!, song.songName)}
              className="group relative p-5 rounded-2xl border-2 transition-all duration-300 border-blue-200 bg-white hover:border-blue-300 hover:shadow-lg hover:shadow-blue-100 hover:-translate-y-1 block w-full text-left"
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                  <span className="text-white font-bold text-sm">WAV</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-gray-900">WAV Audio</h4>
                    <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                      Chất lượng cao
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Chất lượng gốc, không nén, dành cho chuyên nghiệp
                  </p>
                </div>
                <div className="text-blue-500">
                  <Download className="w-5 h-5" />
                </div>
              </div>
            </button>
          ) : (
            <div className="group relative p-5 rounded-2xl border-2 transition-all duration-300 border-gray-200 bg-gray-50 cursor-not-allowed opacity-60">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                  <span className="text-white font-bold text-sm">WAV</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-gray-900">WAV Audio</h4>
                    <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                      Chất lượng cao
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Không có sẵn
                  </p>
                </div>
                <div className="text-gray-400">
                  <Download className="w-5 h-5" />
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const MusicPlayer: React.FC = () => {
  const {
    currentSong,
    isPlaying,
    volume,
    nextSong,
    previousSong,
    togglePlayPause,
  } = useMusicPlayer();

  const user = useUser();
  const isAuthenticated = useIsAuthenticated();
  const { showLoginDialog } = useLoginDialogStore();
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showFormatDialog, setShowFormatDialog] = useState(false);

  // Initialize audio
  useEffect(() => {
    if (!currentSong || !audioRef.current) return;

    const audio = audioRef.current;
    const audioUrl = currentSong.mp3Url || currentSong.fileUrl;

    setIsLoading(true);
    audio.src = audioUrl;
    audio.volume = volume;

    const handleLoadedData = () => {
      setIsLoading(false);
      setDuration(audio.duration);
      if (isPlaying) {
        audio.play().catch(console.error);
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      nextSong();
    };

    const handleError = () => {
      console.error('Audio error');
      toast.error('Không thể phát nhạc');
      setIsLoading(false);
    };

    audio.addEventListener('loadeddata', handleLoadedData);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadeddata', handleLoadedData);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [currentSong]);

  useEffect(() => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.play().catch(console.error);
    } else {
      audioRef.current.pause();
    }
  }, [isPlaying]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTierString = (tier: any): string => {
    if (typeof tier === 'string') return tier;
    if (typeof tier === 'number') {
      switch (tier) {
        case 1: return 'FREE';
        case 2: return 'PRO';
        case 3: return 'BUSINESS';
        default: return 'FREE';
      }
    }
    return 'FREE';
  };

  const canDownload = () => {
    if (!isAuthenticated || !user || !currentSong || user.tier === undefined || user.tier === null) return true;

    const userTier = getTierString(user.tier);
    const songTier = getTierString(currentSong.tier);

    const tierHierarchy: { [key: string]: number } = { 'FREE': 1, 'PRO': 2, 'BUSINESS': 3 };

    return tierHierarchy[userTier] >= tierHierarchy[songTier];
  };

  const handleDownload = () => {
    if (!currentSong) return;

    if (!isAuthenticated) {
      showLoginDialog('Bạn cần đăng nhập để tải xuống bài hát');
      return;
    }

    if (!canDownload()) {
      const songTierString = getTierString(currentSong.tier);
      toast.error(`Bạn cần nâng cấp lên gói ${songTierString} để tải xuống bài hát này`);
      return;
    }

    setShowFormatDialog(true);
  };

  const handleSeek = (value: number[]) => {
    const newTime = value[0];
    setCurrentTime(newTime);
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
    }
  };

  if (!currentSong) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 md:left-56 rounded-2xl m-4 bg-black border-t border-gray-200 shadow-lg z-50">
      <audio ref={audioRef} preload="metadata" />
      <div className="flex items-center px-4 py-2 gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={previousSong}
          className="h-8 w-8 p-0 text-white hover:bg-gray-800"
        >
          <SkipBack className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlayPause}
          disabled={isLoading}
          className="h-10 w-10 p-0 text-white hover:bg-gray-800 rounded-full"
        >
          {isLoading ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : isPlaying ? (
            <Pause className="w-5 h-5" />
          ) : (
            <Play className="w-5 h-5" />
          )}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={nextSong}
          className="h-8 w-8 p-0 text-white hover:bg-gray-800"
        >
          <SkipForward className="w-5 h-5" />
        </Button>

        <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center flex-shrink-0 ml-2">
          {currentSong.imageUrl ? (
            <img
              src={currentSong.imageUrl}
              alt={currentSong.songName}
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <Music className="w-6 h-6 text-white" />
          )}
        </div>

        <div className="min-w-0 flex-1 max-w-[200px]">
          <h4 className="font-semibold text-white truncate text-sm">
            {currentSong.songName}
          </h4>
          <p className="text-gray-300 text-xs truncate uppercase">
            SONBEAT
          </p>
        </div>

        <div className="text-white text-sm font-medium min-w-[40px]">
          {formatTime(currentTime)}
        </div>

        <div className="flex-1 mx-4">
          <Slider
            value={[currentTime]}
            onValueChange={handleSeek}
            max={duration || 100}
            step={1}
            className="w-full [&_[role=slider]]:bg-[#B1FF0A] [&_[role=slider]]:border-[#B1FF0A] [&_.bg-primary]:bg-[#B1FF0A]"
          />
        </div>

        <div className="text-white text-sm font-medium min-w-[40px]">
          {formatTime(duration)}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 ml-4">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-white hover:bg-gray-800"
          >
            <span className="text-lg">+</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-white hover:bg-gray-800"
          >
            <span className="text-lg">♥</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-white hover:bg-gray-800"
          >
            <span className="text-sm">🔗</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleDownload}
            className="h-8 w-8 p-0 text-white hover:bg-gray-800"
            title={
              !isAuthenticated
                ? 'Đăng nhập để tải xuống'
                : !canDownload()
                  ? `Cần gói ${getTierString(currentSong?.tier)} để tải xuống`
                  : 'Tải xuống bài hát'
            }
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {currentSong && (
        <DownloadFormatDialog
          song={currentSong}
          open={showFormatDialog}
          onOpenChange={setShowFormatDialog}
        />
      )}
    </div>
  );
};
