'use client';

import React, { useState, useEffect } from 'react';
import { userProfile, User } from '@/api/apiAuth';
import { uploadFile } from '@/api/apiUpload';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LogoLoading } from '@/components/ui/logo-loading';
import { toast } from 'sonner';
import {
  User as UserIcon,
  Mail,
  Calendar,
  Edit,
  Save,
  X,
  Camera,
} from 'lucide-react';
import { apiUpdateUser, UpdateUserRequest } from '@/api';

export default function UserProfile() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const [editName, setEditName] = useState('');
  const [editAvatarFile, setEditAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      const profileData = await userProfile();
      setUser(profileData);
      setEditName(profileData.name);
    } catch (err: any) {
      console.error('Error fetching profile:', err);
      setError(err.message || 'Không thể tải thông tin profile');
      toast.error('Không thể tải thông tin profile');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfile();
  }, []);

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Vui lòng chọn file hình ảnh hợp lệ');
        return;
      }
      if (file.size > 5 * 1024 * 1024) { 
        toast.error('Kích thước file không được vượt quá 5MB');
        return;
      }
      setEditAvatarFile(file);
      const previewUrl = URL.createObjectURL(file);
      setAvatarPreview(previewUrl);
    }
  };

  const handleStartEdit = () => {
    if (user) {
      setEditName(user.name);
      setIsEditing(true);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditAvatarFile(null);
    setAvatarPreview(null);
    if (user) {
      setEditName(user.name);
    }
  };

  const handleSaveProfile = async () => {
    if (!user || !editName.trim()) {
      toast.error('Tên không được để trống');
      return;
    }

    try {
      setIsUpdating(true);
      let avatarUrl = user.avatarLink || '';

      if (editAvatarFile) {
        setIsUploading(true);
        toast.info('Đang tải lên avatar...');
        avatarUrl = await uploadFile(editAvatarFile);

        if (!avatarUrl || typeof avatarUrl !== 'string' || avatarUrl.trim() === '') {
          throw new Error('Upload avatar thất bại - URL không hợp lệ');
        }


        setIsUploading(false);
      }

      const updateData: UpdateUserRequest = {
        name: editName.trim(),
      };

      if (avatarUrl && avatarUrl.trim() !== '') {
        updateData.avatarLink = avatarUrl;
      }

      const updatedUser = await apiUpdateUser(user.slug, updateData);

      setUser(updatedUser);

      setEditName(updatedUser.name);

      const needsManualUpdate = !updatedUser.name ||
                               updatedUser.name === user.name ||
                               (avatarUrl && avatarUrl.trim() !== '' && updatedUser.avatarLink !== avatarUrl);

      if (needsManualUpdate) {
        const manuallyUpdatedUser = {
          ...user,
          name: editName.trim(),
          ...(avatarUrl && avatarUrl.trim() !== '' && { avatarLink: avatarUrl })
        };

        setUser(manuallyUpdatedUser);
      }

      setIsEditing(false);
      setEditAvatarFile(null);
      setAvatarPreview(null);
      toast.success('Cập nhật thông tin thành công!');

      setTimeout(() => {
        fetchProfile();
      }, 500);
    } catch (err: any) {
      console.error('Error updating profile:', err);
      toast.error(err.message || 'Không thể cập nhật thông tin');
    } finally {
      setIsUpdating(false);
      setIsUploading(false);
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) {
      return 'Chưa có thông tin';
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Ngày không hợp lệ';
    }

    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LogoLoading message="Đang tải thông tin profile..." size="lg" />
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <UserIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Không thể tải profile
              </h3>
              <p className="text-gray-500 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Thử lại
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  console.log("user", user);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="w-full mx-auto">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">Thông tin tài khoản</CardTitle>
                {!isEditing ? (
                  <Button variant="outline" size="sm" onClick={handleStartEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Chỉnh sửa
                  </Button>
                ) : (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancelEdit}
                      disabled={isUpdating}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Hủy
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSaveProfile}
                      disabled={isUpdating || isUploading}
                    >
                      {isUpdating || isUploading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          {isUploading ? 'Đang tải...' : 'Đang lưu...'}
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Lưu
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <Avatar className="h-24 w-24">
                    <AvatarImage
                      src={avatarPreview || user.avatarLink}
                      alt={user.name}
                    />
                    <AvatarFallback className="text-2xl">
                      {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  {isEditing && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center cursor-pointer hover:bg-opacity-60 transition-colors">
                      <Camera className="h-6 w-6 text-white" />
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  {isEditing ? (
                    <div className="space-y-2">
                      <Label htmlFor="name">Tên hiển thị</Label>
                      <Input
                        id="name"
                        value={editName}
                        onChange={(e) => setEditName(e.target.value)}
                        placeholder="Nhập tên của bạn"
                        disabled={isUpdating}
                      />
                    </div>
                  ) : (
                    <>
                      <h2 className="text-2xl font-semibold text-gray-900">
                        {user.name}
                      </h2>
                      <p className="text-gray-600 flex items-center mt-1">
                        <Mail className="h-4 w-4 mr-2" />
                        {user.email}
                      </p>
                    </>
                  )}
                </div>
              </div>
              <Separator />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-500">
                    Hết hạn gói thành viên
                  </label>
                  {user.tierExpiry && (
                    <p className="mt-1 text-gray-900 flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      {formatDate(user.tierExpiry)}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
