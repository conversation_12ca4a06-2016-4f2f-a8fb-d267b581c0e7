'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Artist } from '@/api/apiArtist';

interface ColumnProps {
  editFunction: (artist: Artist) => void;
  deleteFunction: (artist: Artist) => void;
}

export const getAdminArtistColumns = ({ editFunction, deleteFunction }: ColumnProps): ColumnDef<Artist>[] => [
  {
    accessorKey: 'name',
    header: 'Tên',
    cell: ({ row }) => {
      const artist = row.original;
      return (
        <div className="font-medium">
          {artist.name}
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      const artist = row.original;
      return (
        <div className="text-gray-600">
          {artist.email}
        </div>
      );
    },
  },
  {
    accessorKey: 'biography',
    header: 'Tiểu sử',
    cell: ({ row }) => {
      const artist = row.original;
      return (
        <div className="max-w-[300px] truncate text-gray-600">
          {artist.biography}
        </div>
      );
    },
  },
  {
    accessorKey: 'birthdate',
    header: 'Ngày sinh',
    cell: ({ row }) => {
      const artist = row.original;
      return (
        <div className="text-sm text-gray-500">
          {new Date(artist.birthdate).toLocaleDateString('vi-VN')}
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: 'Thao tác',
    cell: ({ row }) => {
      const artist = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Mở menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => editFunction(artist)}>
              <Edit className="mr-2 h-4 w-4" />
              Chỉnh sửa
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => deleteFunction(artist)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Xóa
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
