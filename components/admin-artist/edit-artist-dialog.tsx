'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { apiUpdateArtist, Artist, UpdateArtistData } from '@/api/apiArtist';
import { toast } from 'sonner';

interface EditArtistDialogProps {
  artist: Artist | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function EditArtistDialog({ artist, open, onOpenChange, onSuccess }: EditArtistDialogProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<UpdateArtistData>({
    name: '',
    email: '',
    biography: '',
    birthdate: '',
  });

  // Update form data when artist changes
  useEffect(() => {
    if (artist) {
      setFormData({
        name: artist.name,
        email: artist.email,
        biography: artist.biography,
        birthdate: artist.birthdate,
      });
    }
  }, [artist]);

  const handleInputChange = (field: keyof UpdateArtistData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!artist) return;

    if (!formData.name.trim()) {
      toast.error('Vui lòng nhập tên nghệ sĩ');
      return;
    }

    if (!formData.email.trim()) {
      toast.error('Vui lòng nhập email nghệ sĩ');
      return;
    }

    if (!formData.biography.trim()) {
      toast.error('Vui lòng nhập tiểu sử nghệ sĩ');
      return;
    }

    if (!formData.birthdate.trim()) {
      toast.error('Vui lòng chọn ngày sinh');
      return;
    }

    try {
      setLoading(true);
      await apiUpdateArtist(artist.slug, formData);
      toast.success('Cập nhật nghệ sĩ thành công');
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật nghệ sĩ');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      onOpenChange(newOpen);
    }
  };

  if (!artist) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Chỉnh sửa nghệ sĩ</DialogTitle>
            <DialogDescription>
              Cập nhật thông tin nghệ sĩ. Thay đổi thông tin bên dưới.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Tên nghệ sĩ *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên nghệ sĩ..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-email">Email *</Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Nhập email nghệ sĩ..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-biography">Tiểu sử *</Label>
              <Textarea
                id="edit-biography"
                value={formData.biography}
                onChange={(e) => handleInputChange('biography', e.target.value)}
                placeholder="Nhập tiểu sử nghệ sĩ..."
                rows={3}
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-birthdate">Ngày sinh *</Label>
              <Input
                id="edit-birthdate"
                type="date"
                value={formData.birthdate}
                onChange={(e) => handleInputChange('birthdate', e.target.value)}
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label>Slug</Label>
              <Input
                value={artist.slug}
                disabled
                className="bg-gray-50 text-gray-500"
              />
              <p className="text-xs text-gray-500">
                Slug không thể thay đổi sau khi tạo
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Cập nhật
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
