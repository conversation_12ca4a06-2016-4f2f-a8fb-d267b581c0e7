'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader2, AlertTriangle } from 'lucide-react';
import { apiDeleteArtist, Artist } from '@/api/apiArtist';
import { toast } from 'sonner';

interface DeleteArtistDialogProps {
  artist: Artist | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function DeleteArtistDialog({ artist, open, onOpenChange, onSuccess }: DeleteArtistDialogProps) {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!artist) return;

    try {
      setLoading(true);
      await apiDeleteArtist(artist.slug);
      toast.success('<PERSON><PERSON><PERSON> nghệ sĩ thành công');
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi xóa nghệ sĩ');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      onOpenChange(newOpen);
    }
  };

  if (!artist) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Xác nhận xóa nghệ sĩ
          </DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn xóa nghệ sĩ này không? Hành động này không thể hoàn tác.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div>
              <span className="font-medium text-gray-700">Tên:</span>
              <span className="ml-2">{artist.name}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Email:</span>
              <span className="ml-2">{artist.email}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Slug:</span>
              <span className="ml-2 font-mono text-sm">{artist.slug}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Tiểu sử:</span>
              <span className="ml-2">{artist.biography}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Ngày sinh:</span>
              <span className="ml-2">{new Date(artist.birthdate).toLocaleDateString('vi-VN')}</span>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <strong>Cảnh báo:</strong> Việc xóa nghệ sĩ này có thể ảnh hưởng đến các bài hát đang sử dụng nghệ sĩ này.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={loading}
          >
            Hủy
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xóa nghệ sĩ
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
