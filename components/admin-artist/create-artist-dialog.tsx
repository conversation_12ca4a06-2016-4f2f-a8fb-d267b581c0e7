'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Loader2 } from 'lucide-react';
import { apiCreateArtist, CreateArtistData } from '@/api/apiArtist';
import { toast } from 'sonner';

interface CreateArtistDialogProps {
  onArtistCreated: () => void;
}

export function CreateArtistDialog({ onArtistCreated }: CreateArtistDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateArtistData>({
    name: '',
    email: '',
    biography: '',
    birthdate: '',
  });

  const handleInputChange = (field: keyof CreateArtistData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Vui lòng nhập tên nghệ sĩ');
      return;
    }

    if (!formData.email.trim()) {
      toast.error('Vui lòng nhập email nghệ sĩ');
      return;
    }

    if (!formData.biography.trim()) {
      toast.error('Vui lòng nhập tiểu sử nghệ sĩ');
      return;
    }

    if (!formData.birthdate.trim()) {
      toast.error('Vui lòng chọn ngày sinh');
      return;
    }

    try {
      setLoading(true);
      await apiCreateArtist(formData);
      toast.success('Tạo nghệ sĩ thành công');
      setOpen(false);
      setFormData({ name: '', email: '', biography: '', birthdate: '' });
      onArtistCreated();
    } catch (error: any) {
      toast.error(error.message || 'Có lỗi xảy ra khi tạo nghệ sĩ');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!loading) {
      setOpen(newOpen);
      if (!newOpen) {
        setFormData({ name: '', email: '', biography: '', birthdate: '' });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Thêm nghệ sĩ
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Thêm nghệ sĩ mới</DialogTitle>
            <DialogDescription>
              Tạo nghệ sĩ mới cho hệ thống. Điền thông tin bên dưới.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Tên nghệ sĩ *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên nghệ sĩ..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Nhập email nghệ sĩ..."
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="biography">Tiểu sử *</Label>
              <Textarea
                id="biography"
                value={formData.biography}
                onChange={(e) => handleInputChange('biography', e.target.value)}
                placeholder="Nhập tiểu sử nghệ sĩ..."
                rows={3}
                disabled={loading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="birthdate">Ngày sinh *</Label>
              <Input
                id="birthdate"
                type="date"
                value={formData.birthdate}
                onChange={(e) => handleInputChange('birthdate', e.target.value)}
                disabled={loading}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Tạo nghệ sĩ
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
