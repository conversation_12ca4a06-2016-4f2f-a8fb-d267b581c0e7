'use client';

import React, { useState, useEffect } from 'react';
import { DataTable } from '@/components/data-table';
import { getAdminArtistColumns } from './columns';
import { EditArtistDialog } from './edit-artist-dialog';
import { DeleteArtistDialog } from './delete-artist-dialog';
import { CreateArtistDialog } from './create-artist-dialog';
import { apiGetArtists, Artist } from '@/api/apiArtist';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Mic } from 'lucide-react';

export default function AdminArtistTable() {
  const [artists, setArtists] = useState<Artist[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editArtist, setEditArtist] = useState<Artist | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteArtist, setDeleteArtist] = useState<Artist | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  const [currentPage, setCurrentPage] = useState(1);
  const [totalArtists, setTotalArtists] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [pageSize] = useState(10); // Fixed page size

  const fetchArtists = async (page: number = currentPage, search: string = searchQuery) => {
    try {
      setLoading(true);
      setError(null);
      const searchParam = search.trim();
      const response = await apiGetArtists({
        page,
        limit: pageSize,
        ...(searchParam && { search: searchParam }),
      });
      setArtists(response.items);
      setTotalArtists(response.total);
    } catch (err) {
      setError('Không thể tải danh sách nghệ sĩ');
      toast.error('Không thể tải danh sách nghệ sĩ');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle retry
  const handleRetry = () => {
    fetchArtists();
  };

  // Handle edit artist
  const handleEditArtist = (artist: Artist) => {
    setEditArtist(artist);
    setEditDialogOpen(true);
  };

  // Handle delete artist
  const handleDeleteArtist = (artist: Artist) => {
    setDeleteArtist(artist);
    setDeleteDialogOpen(true);
  };

  // Load data on component mount and when page/search changes
  useEffect(() => {
    fetchArtists(currentPage, searchQuery);
  }, [currentPage, searchQuery]);

  // Get columns with action handlers
  const columns = getAdminArtistColumns({
    editFunction: handleEditArtist,
    deleteFunction: handleDeleteArtist,
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            Quản lý nghệ sĩ
          </CardTitle>
          <CardDescription>
            Quản lý các nghệ sĩ trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Đang tải dữ liệu...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Mic className="h-5 w-5" />
            Lỗi tải dữ liệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Thử lại
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 pb-20">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Quản lý nghệ sĩ
              </CardTitle>
            </div>
            <CreateArtistDialog onArtistCreated={() => fetchArtists()} />
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={artists}
            manualPagination={true}
            totalItems={totalArtists}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={handlePageChange}
            onSearchChange={handleSearch}
          />
        </CardContent>

        <EditArtistDialog
          artist={editArtist}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={() => fetchArtists()}
        />

        <DeleteArtistDialog
          artist={deleteArtist}
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          onSuccess={() => fetchArtists()}
        />
      </Card>
    </div>
  );
}
