import { useState, useCallback } from 'react';
export interface SongFilters {
  selectedGenre: string;
  selectedMood: string;
  searchQuery: string;
  currentPage: number;
  pageSize: number;
}

export function useSongFilters() {
  const [selectedGenre, setSelectedGenre] = useState<string>('');
  const [selectedMood, setSelectedMood] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const clearAllFilters = useCallback(() => {
    setSelectedGenre('');
    setSelectedMood('');
    setSearchQuery('');
    setCurrentPage(1);
  }, []);

  const resetToFirstPage = useCallback(() => {
    setCurrentPage(1);
  }, []);

  const updateGenre = useCallback((genre: string) => {
    setSelectedGenre(genre);
    setCurrentPage(1);
  }, []);

  const updateMood = useCallback((mood: string) => {
    setSelectedMood(mood);
    setCurrentPage(1);
  }, []);

  const updateSearch = useCallback((search: string) => {
    setSearchQuery(search);
    setCurrentPage(1);
  }, []);

  const updatePageSize = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  }, []);

  const hasActiveFilters = selectedGenre !== '' || selectedMood !== '' || searchQuery.trim() !== '';

  return {
    // State
    selectedGenre,
    selectedMood,
    searchQuery,
    currentPage,
    pageSize,
    hasActiveFilters,
    
    // Actions
    setSelectedGenre,
    setSelectedMood,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    clearAllFilters,
    resetToFirstPage,
    updateGenre,
    updateMood,
    updateSearch,
    updatePageSize,
  };
}
