import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useMemo } from 'react';

export interface UrlParams {
  page: number;
  genreSlug: string;
  moodSlug: string;
  artistSlug: string;
  search: string;
  tier: string;
  limit: number;
}

export function useUrlParams() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const currentParams = useMemo((): UrlParams => ({
    page: parseInt(searchParams.get('page') || '1', 10),
    genreSlug: searchParams.get('genreSlug') || searchParams.get('genre') || '',
    moodSlug: searchParams.get('moodSlug') || searchParams.get('mood') || '',
    artistSlug: searchParams.get('artistSlug') || searchParams.get('artist') || '',
    search: searchParams.get('search') || '',
    tier: searchParams.get('tier') || '',
    limit: parseInt(searchParams.get('limit') || '10', 10),
  }), [searchParams]);

  const updateParams = useCallback((newParams: Partial<UrlParams>) => {
    const params = new URLSearchParams(searchParams.toString());
    
    const updatedParams = { ...currentParams, ...newParams };
    
    Object.entries(updatedParams).forEach(([key, value]) => {
      if (value && value !== '' && value !== 0) {
        params.set(key, value.toString());
      } else if (key !== 'page' && key !== 'limit') {
        params.delete(key);
      }
    });
    
    if (!params.get('page') || parseInt(params.get('page') || '1') < 1) {
      params.set('page', '1');
    }
    
    if (!params.get('limit')) {
      params.set('limit', '10');
    }
    
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.push(newUrl, { scroll: false });
  }, [router, searchParams, currentParams]);

  const updatePage = useCallback((page: number) => {
    updateParams({ page });
  }, [updateParams]);

  const updateGenre = useCallback((genreSlug: string) => {
    updateParams({ genreSlug, page: 1 }); 
  }, [updateParams]);

  const updateMood = useCallback((moodSlug: string) => {
    updateParams({ moodSlug, page: 1 });
  }, [updateParams]);

  const updateArtist = useCallback((artistSlug: string) => {
    updateParams({ artistSlug, page: 1 });
  }, [updateParams]);

  const updateSearch = useCallback((search: string) => {
    updateParams({ search, page: 1 });
  }, [updateParams]);

  const updateTiger = useCallback((tier: string) => {
    updateParams({ tier, page: 1 });
  }, [updateParams]);

  const updateLimit = useCallback((limit: number) => {
    updateParams({ limit, page: 1 });
  }, [updateParams]);

  const clearFilters = useCallback(() => {
    updateParams({ genreSlug: '', moodSlug: '', artistSlug: '', search: '', tier: '', page: 1 });
  }, [updateParams]);

  const hasActiveFilters = useMemo(() => {
    return currentParams.genreSlug !== '' ||
           currentParams.moodSlug !== '' ||
           currentParams.artistSlug !== '' ||
           currentParams.search !== '' ||
           currentParams.tier !== '';
  }, [currentParams]);

  return {
    currentParams,

    page: currentParams.page,
    genre: currentParams.genreSlug,
    mood: currentParams.moodSlug,
    artist: currentParams.artistSlug,
    search: currentParams.search,
    tiger: currentParams.tier,
    limit: currentParams.limit,

    hasActiveFilters,

    updateParams,
    updatePage,
    updateGenre,
    updateMood,
    updateArtist,
    updateSearch,
    updateTiger,
    updateLimit,
    clearFilters,
  };
}
