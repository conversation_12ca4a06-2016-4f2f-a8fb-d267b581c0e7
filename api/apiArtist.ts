import api from './axios'

export interface Artist {
  id: number
  slug: string
  name: string
  email: string
  biography: string
  birthdate: string
  createdAt: string
  updatedAt?: string
  createdBy?: string | null
  updatedBy?: string | null
}

export interface ArtistApiResponse {
  items: Artist[]
  total: number
}

export interface GetArtistsParams {
  search?: string
  limit?: number
  page?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface CreateArtistData {
  name: string
  email: string
  biography: string
  birthdate: string
}

export interface UpdateArtistData {
  name: string
  email: string
  biography: string
  birthdate: string
}

// Get all artists with pagination and search
export const apiGetArtists = async (params?: Partial<GetArtistsParams>): Promise<ArtistApiResponse> => {
  try {
    const response = await api.get<ArtistApiResponse>('/artist', { params })
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch artists')
  }
}

// Get single artist by slug
export const apiGetArtist = async (slug: string): Promise<Artist> => {
  try {
    const response = await api.get<Artist>(`/artist/${slug}`)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch artist')
  }
}

// Create new artist
export const apiCreateArtist = async (data: CreateArtistData): Promise<Artist> => {
  try {
    const response = await api.post<Artist>('/artist', data)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to create artist')
  }
}

// Update artist by slug
export const apiUpdateArtist = async (slug: string, data: UpdateArtistData): Promise<Artist> => {
  try {
    const response = await api.patch<Artist>(`/artist/${slug}`, data)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update artist')
  }
}

// Delete artist by slug
export const apiDeleteArtist = async (slug: string): Promise<void> => {
  try {
    await api.delete(`/artist/${slug}`)
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete artist')
  }
}
