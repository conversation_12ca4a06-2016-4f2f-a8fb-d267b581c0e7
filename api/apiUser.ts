import api from './axios'
import { User } from './apiAuth'

export interface UserApiResponse {
  items: User[]
  total: number
}

export interface UpdateUserRequest {
  name?: string
  email?: string
  tier?: 'FREE' | 'PRO' | 'BUSINESS'
  roleLst?: string[]
  avatarLink?: string
}

export const apiGetUsers = async (params?: {
  search?: string
  limit?: number
  page?: number
  role?: string
}): Promise<UserApiResponse> => {
  try {
    const response = await api.get<UserApiResponse>('/user', { params })
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch users')
  }
}

export const apiGetUserBySlug = async (slug: string): Promise<User> => {
  try {
    const response = await api.get<User>(`/user/${slug}`)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch user')
  }
}


export const apiUpdateUser = async (slug: string, userData: UpdateUserRequest): Promise<User> => {
  try {
    const response = await api.patch<User>(`/user/${slug}`, userData);
    return response.data;
  } catch (error: any) {
    if (error.response?.data?.message) {
      if (Array.isArray(error.response.data.message)) {
        throw new Error(error.response.data.message.join(', '));
      } else {
        throw new Error(error.response.data.message);
      }
    } else {
      throw new Error(error.message || 'Failed to update user');
    }
  }
}
