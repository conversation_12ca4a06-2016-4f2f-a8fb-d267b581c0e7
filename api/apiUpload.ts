import api from "./axios";

export async function uploadFile(file: File, _onProgress?: (progress: number) => void): Promise<string> {
  if (!file) {
    throw new Error("No file selected");
  }

  const formData = new FormData();
  formData.append("file", file);

  const response = await api.post("/upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  if (response.data && typeof response.data === 'object' && response.data.url) {
    return response.data.url;
  }

  if (typeof response.data === 'string') {
    return response.data;
  }

  throw new Error('Invalid upload response format');
}