export { default as api } from './axios'
export { 
  getAuthToken, 
  setAuthToken, 
  setRefreshToken, 
  clearTokens, 
  isAuthenticated 
} from './axios'

export {
  apiLogin,
  userRegister,
  userLogout,
  type LoginRequest,
  type RegisterRequest,
  type AuthResponse,
  type User
} from './apiAuth'

export {
  apiGetUsers,
  apiGetUserBySlug,
  apiUpdateUser,
  type UserApiResponse,
  type UpdateUserRequest
} from './apiUser'

export {
  apiGetSongs,
  apiGetAllSongs,
  apiGetSongBySlug,
  apiCreateSong,
  apiUpdateSong,
  apiDeleteSong,
  type Song,
  type SongApiResponse,
  type GetSongsParams,
  type UpdateSongRequest
} from './apiSong'

export {
  uploadFile
} from './apiUpload'

export {
  apiGetMoods,
  apiGetMood,
  apiCreateMood,
  apiUpdateMood,
  apiDeleteMood,
  type Mood,
  type MoodApiResponse,
  type GetMoodsParams,
  type CreateMoodData,
  type UpdateMoodData
} from './apiMood'

export {
  apiGetGenres,
  apiGetGenre,
  apiCreateGenre,
  apiUpdateGenre,
  apiDeleteGenre,
  type Genre,
  type GenreApiResponse,
  type GetGenresParams,
  type CreateGenreData,
  type UpdateGenreData
} from './apiGenre'

export {
  apiGetArtists,
  apiGetArtist,
  apiCreateArtist,
  apiUpdateArtist,
  apiDeleteArtist,
  type Artist,
  type ArtistApiResponse,
  type GetArtistsParams,
  type CreateArtistData,
  type UpdateArtistData
} from './apiArtist'
