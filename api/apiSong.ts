import api from './axios'
import { Artist } from './apiArtist'
import { Genre } from './apiGenre'
import { Mood } from './apiMood'

export interface Song {
  id: number
  slug: string
  songName: string
  artistName?: string
  genre?: string
  mood?: string
  tags: string[]
  artistLst: Artist[]
  genreLst: Genre[]
  moodLst: Mood[]
  tempo: number
  duration: number
  pitch: string
  tier: 'FREE' | 'PRO' | 'BUSINESS'
  fileUrl: string
  mp3Url: string
  imageUrl: string
  createdAt: string
  updatedAt: string
  createdBy?: number | null
  updatedBy?: number | null
}

export interface SongApiResponse {
  items: Song[]
  total: number
}

export interface GetSongsParams {
  search?: string
  limit?: number
  page?: number
  moodSlug?: string
  genreSlug?: string
  artistSlug?: string
}

export interface GetSongsInfoResponse {
  tempoBpm: number;
  averagePitchHz: number;
  averagePitchNote: string;
  durationSeconds: number;
}

export const apiGetSongs = async (params?: Partial<GetSongsParams>): Promise<SongApiResponse> => {
  try {
    const response = await api.get<SongApiResponse>('/song', { params })
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch songs')
  }
}

export const apiGetAllSongs = async (): Promise<SongApiResponse> => {
  try {
    const response = await api.get<SongApiResponse>('/song')
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch songs')
  }
}

export const apiGetSongBySlug = async (slug: string): Promise<Song> => {
  try {
    const response = await api.get<Song>(`/song/${slug}`)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch song')
  }
}

export interface CreateSongRequest {
  songName: string
  tags: string[]
  artistIds: number[]
  genreIds: number[]
  moodIds: number[]
  tempo: number
  duration: number
  pitch: string
  tier: 'FREE' | 'PRO' | 'BUSINESS'
  fileUrl: string
  mp3Url?: string
  imageUrl: string
}

export const apiCreateSong = async (songData: CreateSongRequest): Promise<Song> => {
  try {
    const response = await api.post<Song>('/song', songData)
    return response.data
  } catch (error: any) {
    // Handle validation errors
    if (error.response?.data?.message) {
      if (Array.isArray(error.response.data.message)) {
        throw new Error(error.response.data.message.join(', '))
      } else {
        throw new Error(error.response.data.message)
      }
    }
    throw new Error(error.message || 'Failed to create song')
  }
}

export interface UpdateSongRequest {
  songName?: string
  tags?: string[]
  artistIds?: number[]
  genreIds?: number[]
  moodIds?: number[]
  tempo?: number
  duration?: number
  pitch?: string
  tier?: 'FREE' | 'PRO' | 'BUSINESS'
  fileUrl?: string
  mp3Url?: string
  imageUrl?: string
}

export const apiUpdateSong = async (slug: string, songData: UpdateSongRequest): Promise<Song> => {
  try {
    const response = await api.patch<Song>(`/song/${slug}`, songData)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update song')
  }
}

export const apiGetSongInfor = (file: File): Promise<GetSongsInfoResponse> => {
  const formData = new FormData();
  formData.append("file", file);

  return api
    .post<any>("/song/get-song-infor", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })
    .then((response) => response.data.data as GetSongsInfoResponse)
    .catch((error) => {
      throw new Error(
        error.response?.data?.message || "Failed to get song info"
      );
    });
};


export const apiDeleteSong = async (slug: string): Promise<void> => {
  try {
    await api.delete(`/song/${slug}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete song');
  }
};
