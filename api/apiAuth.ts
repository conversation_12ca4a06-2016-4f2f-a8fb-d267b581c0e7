import api, { setAuthToken, setRefreshToken } from './axios'
import { useAuthStore } from '@/store/authStore'

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
}

export interface AuthResponse {
  access_token: string
  refresh_token: string
  access_token_expires_at: number
  refresh_token_expires_at: number
  user: User
}

export interface User {
  id: number
  email: string
  name: string
  roleLst: string[]
  tier: 'FREE' | 'PRO' | 'BUSINESS'
  createdAt: string
  updatedAt: string
  tierExpiry: string | null
  slug: string
  createdBy: number | null
  updatedBy: number | null
  password: string
  status: 'active' | 'inactive' | 'pending'
  isActive: boolean
  avatarLink?: string
}


export const apiLogin = async (credentials: LoginRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post('/auth/login', credentials)
    const authData = response.data as AuthResponse

    setAuthToken(authData.access_token)
    setRefreshToken(authData.refresh_token)

    if (typeof document !== 'undefined') {
      document.cookie = `access_token=${authData.access_token}; path=/; max-age=${24 * 60 * 60}` // 24 hours
      document.cookie = `refresh_token=${authData.refresh_token}; path=/; max-age=${7 * 24 * 60 * 60}` // 7 days
    }

    useAuthStore.getState().setAuth(authData)

    return authData
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Login failed')
  }
}


export const userRegister = async (userData: RegisterRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post('/auth/register', userData)
    const authData = response.data as AuthResponse

    setAuthToken(authData.access_token)
    setRefreshToken(authData.refresh_token)

    useAuthStore.getState().setAuth(authData)

    return authData
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Registration failed')
  }
}


export const userLogout = async (): Promise<void> => {
  try {
    await api.post('/auth/logout')
  } catch (error) {
    // Logout API call failed
  } finally {
    setAuthToken('')
    setRefreshToken('')

    if (typeof document !== 'undefined') {
      document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    }

    useAuthStore.getState().clearAuth()
  }
}

export const googleLogin = async (token: string): Promise<AuthResponse> => {
  try {
    const response = await api.post('/auth/google', { token: token })
    const authData = response.data as AuthResponse

    setAuthToken(authData.access_token)
    setRefreshToken(authData.refresh_token)

    if (typeof document !== 'undefined') {
      document.cookie = `access_token=${authData.access_token}; path=/; max-age=${24 * 60 * 60}` // 24 hours
      document.cookie = `refresh_token=${authData.refresh_token}; path=/; max-age=${7 * 24 * 60 * 60}` // 7 days
    }

    useAuthStore.getState().setAuth(authData)

    return authData
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Google login failed')
  }
}


export const userProfile = async (): Promise<User> => {
  try {
    const response = await api.get('/auth/profile')
    return response.data as User
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to get user profile')
  }
}


