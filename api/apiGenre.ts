import api from './axios'

export interface Genre {
  id: number
  slug: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  createdBy: string | null
  updatedBy: string | null
}

export interface GenreApiResponse {
  items: Genre[]
  total: number
}

export interface GetGenresParams {
  search?: string
  limit?: number
  page?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface CreateGenreData {
  name: string
  description: string
}

export interface UpdateGenreData {
  name: string
  description: string
}

// Get all genres with pagination and search
export const apiGetGenres = async (params?: Partial<GetGenresParams>): Promise<GenreApiResponse> => {
  try {
    const response = await api.get<GenreApiResponse>('/genre', { params })
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch genres')
  }
}

// Get single genre by slug
export const apiGetGenre = async (slug: string): Promise<Genre> => {
  try {
    const response = await api.get<Genre>(`/genre/${slug}`)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch genre')
  }
}

// Create new genre
export const apiCreateGenre = async (data: CreateGenreData): Promise<Genre> => {
  try {
    const response = await api.post<Genre>('/genre', data)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to create genre')
  }
}

// Update genre by slug
export const apiUpdateGenre = async (slug: string, data: UpdateGenreData): Promise<Genre> => {
  try {
    const response = await api.patch<Genre>(`/genre/${slug}`, data)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update genre')
  }
}

// Delete genre by slug
export const apiDeleteGenre = async (slug: string): Promise<void> => {
  try {
    await api.delete(`/genre/${slug}`)
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete genre')
  }
}
