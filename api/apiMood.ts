
import api from './axios'

export interface Mood {
  id: number
  slug: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  createdBy: string | null
  updatedBy: string | null
}

export interface MoodApiResponse {
  items: Mood[]
  total: number
}

export interface GetMoodsParams {
  search?: string
  limit?: number
  page?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface CreateMoodData {
  name: string
  description: string
}

export interface UpdateMoodData {
  name: string
  description: string
}

// Get all moods with pagination and search
export const apiGetMoods = async (params?: Partial<GetMoodsParams>): Promise<MoodApiResponse> => {
  try {
    const response = await api.get<MoodApiResponse>('/mood', { params })
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch moods')
  }
}

// Get single mood by slug
export const apiGetMood = async (slug: string): Promise<Mood> => {
  try {
    const response = await api.get<Mood>(`/mood/${slug}`)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch mood')
  }
}

// Create new mood
export const apiCreateMood = async (data: CreateMoodData): Promise<Mood> => {
  try {
    const response = await api.post<Mood>('/mood', data)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to create mood')
  }
}

// Update mood by slug
export const apiUpdateMood = async (slug: string, data: UpdateMoodData): Promise<Mood> => {
  try {
    const response = await api.patch<Mood>(`/mood/${slug}`, data)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update mood')
  }
}

// Delete mood by slug
export const apiDeleteMood = async (slug: string): Promise<void> => {
  try {
    await api.delete(`/mood/${slug}`)
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete mood')
  }
}


