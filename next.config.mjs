/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
    domains: ['cdn.vncm.net', 'api.vncm.net'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.vncm.net',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'api.vncm.net',
        port: '',
        pathname: '/**',
      },
    ],
  },
}

export default nextConfig
